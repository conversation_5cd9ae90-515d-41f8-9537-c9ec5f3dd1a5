// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.model.Admin;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

/**
 * Admin Launcher - Direct access to Admin Dashboard
 */
public class AdminLauncher extends Application {

    private UserService userService;

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("🔄 Đang khởi tạo UserService...");
            this.userService = new UserService();
            System.out.println("✅ UserService đã được khởi tạo thành công!");

            // Initialize default admin if not exists
            System.out.println("🔍 Đang kiểm tra tài khoản Admin mặc định...");
            initializeDefaultAdmin();

            // Get admin user
            System.out.println("🔐 Đang xác thực tài khoản Admin...");
            User admin = userService.getUserById("ADMIN001");
            if (admin == null) {
                System.err.println("❌ ════════════════════════════════════════");
                System.err.println("❌ KHÔNG THỂ TÌM THẤY HOẶC TẠO TÀI KHOẢN ADMIN!");
                System.err.println("❌ ════════════════════════════════════════");
                return;
            }

            System.out.println("✅ Xác thực thành công!");
            System.out.println("🎯 Đang thiết lập SceneManager...");

            // Set up SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            SceneManager.setCurrentUser(admin);

            System.out.println("🚀 Đang khởi động Admin Dashboard...");

            // Launch admin dashboard directly
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml",
                                   "🔧 ADMIN DASHBOARD - Hệ thống quản lý sinh viên");

            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("🎉 ADMIN DASHBOARD ĐÃ KHỞI ĐỘNG THÀNH CÔNG!");
            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("👤 Đăng nhập với tư cách: " + admin.getFullName());
            System.out.println("🔧 Quyền truy cập: Quản trị viên hệ thống");
            System.out.println("📊 Có thể quản lý: Sinh viên, Giáo viên, Khóa học");
            System.out.println("🎯 Sẵn sàng sử dụng các chức năng quản trị!");

        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI KHỞI ĐỘNG ADMIN DASHBOARD!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cấu hình và thử lại!");
            e.printStackTrace();
        }
    }

    private void initializeDefaultAdmin() {
        try {
            if (userService.getUserById("ADMIN001") == null) {
                System.out.println("🆕 Tài khoản Admin chưa tồn tại, đang tạo mới...");

                User admin = new Admin();
                admin.setUserId("ADMIN001");
                admin.setUsername("admin");
                admin.setPassword("admin123");
                admin.setFullName("Quản trị viên hệ thống");
                admin.setEmail("<EMAIL>");
                admin.setPhone("0123456789");
                admin.setRole(User.UserRole.ADMIN);

                userService.addUser(admin);
                System.out.println("🎉 ════════════════════════════════════════");
                System.out.println("🎉 TẠO TÀI KHOẢN ADMIN THÀNH CÔNG!");
                System.out.println("🎉 ════════════════════════════════════════");
                System.out.println("👤 Tên đăng nhập: admin");
                System.out.println("🔐 Mật khẩu: admin123");
                System.out.println("📧 Email: <EMAIL>");
                System.out.println("📱 Điện thoại: 0123456789");
                System.out.println("🎯 Vai trò: Quản trị viên hệ thống");
            } else {
                System.out.println("✅ Tài khoản Admin đã tồn tại, sẵn sàng đăng nhập!");
            }
        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI TẠO TÀI KHOẢN ADMIN MẶC ĐỊNH!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cơ sở dữ liệu!");
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options

        System.out.println("🚀 ════════════════════════════════════════════════════════════");
        System.out.println("🚀           KHỞI ĐỘNG ADMIN DASHBOARD");
        System.out.println("🚀 ════════════════════════════════════════════════════════════");
        System.out.println("🔧 Chế độ: Admin Dashboard (Truy cập trực tiếp)");
        System.out.println("👤 Đăng nhập tự động: Quản trị viên hệ thống");
        System.out.println("🎯 Quyền truy cập: Toàn bộ chức năng quản trị");
        System.out.println("📊 Có thể quản lý:");
        System.out.println("   👨‍🎓 • Quản lý Sinh viên");
        System.out.println("   👨‍🏫 • Quản lý Giáo viên");
        System.out.println("   📚 • Quản lý Khóa học");
        System.out.println("   📈 • Xem báo cáo và thống kê");
        System.out.println("   ⚙️  • Cấu hình hệ thống");
        System.out.println("🚀 ════════════════════════════════════════════════════════════");
        System.out.println("⏳ Đang khởi động ứng dụng...");

        launch(args);
    }
}
package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class GradeEntryDialogController implements Initializable {
    
    @FXML private Label dialogTitle;
    @FXML private ComboBox<String> studentComboBox;
    @FXML private ComboBox<String> courseComboBox;
    @FXML private TextField scoreField;
    @FXML private ComboBox<String> semesterComboBox;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    // Error labels
    @FXML private Label studentErrorLabel;
    @FXML private Label courseErrorLabel;
    @FXML private Label scoreErrorLabel;
    @FXML private Label semesterErrorLabel;
    
    // Score preview
    @FXML private VBox scorePreviewBox;
    @FXML private Label letterGradeLabel;
    @FXML private Label gradePointLabel;
    
    private UserService userService;
    private CourseService courseService;
    private GradeService gradeService;
    private Grade existingGrade;
    private Teacher currentTeacher;
    private boolean isEditMode = false;
    private Grade resultGrade;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        userService = new UserService();
        courseService = new CourseService();
        gradeService = new GradeService();
        
        setupValidation();
        setupScorePreview();
        loadData();
    }
    
    public void setExistingGrade(Grade grade) {
        this.existingGrade = grade;
        this.isEditMode = (grade != null);
        
        if (isEditMode) {
            dialogTitle.setText("Sửa điểm");
            populateFields();
        } else {
            dialogTitle.setText("Nhập điểm mới");
        }
    }
    
    public void setCurrentTeacher(Teacher teacher) {
        this.currentTeacher = teacher;
        loadTeacherCourses();
    }
    
    public Grade getResult() {
        return resultGrade;
    }
    
    private void setupValidation() {
        // Real-time validation for score field
        scoreField.textProperty().addListener((obs, oldText, newText) -> {
            validateScore();
            updateScorePreview();
        });
        
        // Validation for other fields
        studentComboBox.valueProperty().addListener((obs, oldValue, newValue) -> validateStudent());
        courseComboBox.valueProperty().addListener((obs, oldValue, newValue) -> validateCourse());
        semesterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> validateSemester());
    }
    
    private void setupScorePreview() {
        scorePreviewBox.setVisible(false);
    }
    
    private void loadData() {
        // Load semesters
        semesterComboBox.setItems(FXCollections.observableArrayList(
            "2024-1", "2024-2", "2025-1", "2025-2"
        ));
        
        if (!isEditMode) {
            loadStudents();
            loadCourses();
        }
    }
    
    private void loadStudents() {
        try {
            List<User> students = userService.getAllUsers().stream()
                .filter(user -> user instanceof Student)
                .collect(Collectors.toList());
            
            List<String> studentOptions = students.stream()
                .map(student -> student.getUserId() + " - " + student.getFullName())
                .collect(Collectors.toList());
            
            studentComboBox.setItems(FXCollections.observableArrayList(studentOptions));
        } catch (Exception e) {
            showError("Lỗi tải danh sách sinh viên: " + e.getMessage());
        }
    }
    
    private void loadCourses() {
        try {
            List<Course> courses = courseService.getAllCourses();
            List<String> courseOptions = courses.stream()
                .map(course -> course.getCourseId() + " - " + course.getCourseName())
                .collect(Collectors.toList());
            
            courseComboBox.setItems(FXCollections.observableArrayList(courseOptions));
        } catch (Exception e) {
            showError("Lỗi tải danh sách môn học: " + e.getMessage());
        }
    }
    
    private void loadTeacherCourses() {
        if (currentTeacher == null) return;
        
        try {
            List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
            List<String> courseOptions = teacherCourses.stream()
                .map(course -> course.getCourseId() + " - " + course.getCourseName())
                .collect(Collectors.toList());
            
            courseComboBox.setItems(FXCollections.observableArrayList(courseOptions));
        } catch (Exception e) {
            showError("Lỗi tải danh sách môn học: " + e.getMessage());
        }
    }
    
    private void populateFields() {
        if (existingGrade == null) return;
        
        // Find and select student
        String studentOption = existingGrade.getStudentId() + " - ";
        for (String option : studentComboBox.getItems()) {
            if (option.startsWith(studentOption)) {
                studentComboBox.setValue(option);
                break;
            }
        }
        
        // Find and select course
        String courseOption = existingGrade.getCourseId() + " - ";
        for (String option : courseComboBox.getItems()) {
            if (option.startsWith(courseOption)) {
                courseComboBox.setValue(option);
                break;
            }
        }
        
        scoreField.setText(String.valueOf(existingGrade.getScore()));
        semesterComboBox.setValue(existingGrade.getSemester());
        
        // Disable student and course selection in edit mode
        studentComboBox.setDisable(true);
        courseComboBox.setDisable(true);
        semesterComboBox.setDisable(true);
    }
    
    private boolean validateScore() {
        String scoreText = scoreField.getText().trim();
        
        if (scoreText.isEmpty()) {
            showScoreError("Vui lòng nhập điểm số");
            return false;
        }
        
        try {
            double score = Double.parseDouble(scoreText);
            if (score < 0 || score > 10) {
                showScoreError("Điểm số phải từ 0 đến 10");
                return false;
            }
            
            // Check decimal places (max 1 decimal place)
            if (scoreText.contains(".")) {
                String[] parts = scoreText.split("\\.");
                if (parts.length > 1 && parts[1].length() > 1) {
                    showScoreError("Điểm số chỉ được có tối đa 1 chữ số thập phân");
                    return false;
                }
            }
            
            hideScoreError();
            return true;
        } catch (NumberFormatException e) {
            showScoreError("Điểm số không hợp lệ");
            return false;
        }
    }
    
    private boolean validateStudent() {
        if (studentComboBox.getValue() == null || studentComboBox.getValue().isEmpty()) {
            showStudentError("Vui lòng chọn sinh viên");
            return false;
        }
        hideStudentError();
        return true;
    }
    
    private boolean validateCourse() {
        if (courseComboBox.getValue() == null || courseComboBox.getValue().isEmpty()) {
            showCourseError("Vui lòng chọn môn học");
            return false;
        }
        hideCourseError();
        return true;
    }
    
    private boolean validateSemester() {
        if (semesterComboBox.getValue() == null || semesterComboBox.getValue().isEmpty()) {
            showSemesterError("Vui lòng chọn học kỳ");
            return false;
        }
        hideSemesterError();
        return true;
    }
    
    private void updateScorePreview() {
        if (!validateScore()) {
            scorePreviewBox.setVisible(false);
            return;
        }
        
        try {
            double score = Double.parseDouble(scoreField.getText().trim());
            
            // Create temporary grade to calculate letter grade
            Grade tempGrade = new Grade();
            tempGrade.setScore(score);
            
            letterGradeLabel.setText(tempGrade.getLetterGrade());
            gradePointLabel.setText(String.format("%.1f", tempGrade.getGradePoint()));
            scorePreviewBox.setVisible(true);
            
        } catch (Exception e) {
            scorePreviewBox.setVisible(false);
        }
    }
    
    @FXML
    private void handleSave() {
        if (!validateAllFields()) {
            return;
        }
        
        try {
            Grade grade = existingGrade != null ? existingGrade : new Grade();
            
            if (!isEditMode) {
                // Parse student ID
                String studentOption = studentComboBox.getValue();
                String studentId = studentOption.split(" - ")[0];
                
                // Parse course ID and get course info
                String courseOption = courseComboBox.getValue();
                String courseId = courseOption.split(" - ")[0];
                Course course = courseService.getCourseById(courseId);
                
                grade.setStudentId(studentId);
                grade.setCourseId(courseId);
                grade.setCourseName(course.getCourseName());
                grade.setCredits(course.getCredits());
                grade.setSemester(semesterComboBox.getValue());
                grade.setTeacherId(currentTeacher != null ? currentTeacher.getTeacherId() : course.getTeacherId());
            }
            
            // Set score (this will automatically calculate letter grade)
            double score = Double.parseDouble(scoreField.getText().trim());
            grade.setScore(score);
            
            resultGrade = grade;
            closeDialog();
            
        } catch (Exception e) {
            showError("Lỗi khi lưu điểm: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleCancel() {
        resultGrade = null;
        closeDialog();
    }
    
    private boolean validateAllFields() {
        boolean valid = true;
        
        if (!isEditMode) {
            valid &= validateStudent();
            valid &= validateCourse();
            valid &= validateSemester();
        }
        valid &= validateScore();
        
        return valid;
    }
    
    private void showScoreError(String message) {
        scoreErrorLabel.setText("⚠️ " + message);
        scoreErrorLabel.setVisible(true);
        scoreField.getStyleClass().add("error-state");
    }

    private void hideScoreError() {
        scoreErrorLabel.setVisible(false);
        scoreField.getStyleClass().remove("error-state");
    }

    private void showStudentError(String message) {
        studentErrorLabel.setText("⚠️ " + message);
        studentErrorLabel.setVisible(true);
        studentComboBox.getStyleClass().add("error-state");
    }

    private void hideStudentError() {
        studentErrorLabel.setVisible(false);
        studentComboBox.getStyleClass().remove("error-state");
    }

    private void showCourseError(String message) {
        courseErrorLabel.setText("⚠️ " + message);
        courseErrorLabel.setVisible(true);
        courseComboBox.getStyleClass().add("error-state");
    }

    private void hideCourseError() {
        courseErrorLabel.setVisible(false);
        courseComboBox.getStyleClass().remove("error-state");
    }

    private void showSemesterError(String message) {
        semesterErrorLabel.setText("⚠️ " + message);
        semesterErrorLabel.setVisible(true);
        semesterComboBox.getStyleClass().add("error-state");
    }

    private void hideSemesterError() {
        semesterErrorLabel.setVisible(false);
        semesterComboBox.getStyleClass().remove("error-state");
    }
    
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Lỗi");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void closeDialog() {
        Stage stage = (Stage) saveButton.getScene().getWindow();
        stage.close();
    }
}

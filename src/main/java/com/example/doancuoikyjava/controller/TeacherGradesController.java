package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class TeacherGradesController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button addGradeBtn;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    
    @FXML private ComboBox<String> courseFilterComboBox;
    @FXML private ComboBox<String> semesterFilterComboBox;
    @FXML private TextField searchField;
    
    @FXML private TableView<Grade> gradesTableView;
    @FXML private TableColumn<Grade, String> studentIdColumn;
    @FXML private TableColumn<Grade, String> studentNameColumn;
    @FXML private TableColumn<Grade, String> courseNameColumn;
    @FXML private TableColumn<Grade, Integer> creditsColumn;
    @FXML private TableColumn<Grade, Double> scoreColumn;
    @FXML private TableColumn<Grade, String> letterGradeColumn;
    @FXML private TableColumn<Grade, String> semesterColumn;
    @FXML private TableColumn<Grade, LocalDate> dateColumn;
    @FXML private TableColumn<Grade, Void> actionsColumn;
    
    @FXML private Label totalGradesLabel;
    @FXML private Label averageGradeLabel;
    
    private GradeService gradeService;
    private UserService userService;
    private CourseService courseService;
    private Teacher currentTeacher;
    private ObservableList<Grade> allGrades;
    private ObservableList<Grade> filteredGrades;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        gradeService = new GradeService();
        userService = new UserService();
        courseService = new CourseService();
        allGrades = FXCollections.observableArrayList();
        filteredGrades = FXCollections.observableArrayList();

        setupCurrentTeacher();
        setupWelcomeMessage();
        setupModernUI();
        setupTableColumns();
        setupFilters();
        loadGrades();
    }

    private void setupModernUI() {
        // Apply modern CSS - delay until scene is ready
        Platform.runLater(() -> {
            try {
                if (gradesTableView.getScene() != null) {
                    String cssPath = getClass().getResource("/com/example/doancuoikyjava/styles/teacher-grades.css").toExternalForm();
                    gradesTableView.getScene().getStylesheets().add(cssPath);
                    System.out.println("✅ Teacher grades CSS loaded successfully");
                } else {
                    System.out.println("⚠️ Scene not ready yet, will apply CSS later");
                }
            } catch (Exception e) {
                System.err.println("⚠️ Could not load teacher-grades.css: " + e.getMessage());
                e.printStackTrace();
            }
        });

        // Setup modern button styles
        try {
            addGradeBtn.getStyleClass().addAll("modern-action-btn", "primary-action");
            refreshBtn.getStyleClass().addAll("modern-action-btn", "secondary-action");
            searchBtn.getStyleClass().add("modern-search-btn");
            exportBtn.getStyleClass().add("modern-export-btn");

            // Setup modern input styles
            courseFilterComboBox.getStyleClass().add("modern-filter-combo");
            semesterFilterComboBox.getStyleClass().add("modern-filter-combo");
            searchField.getStyleClass().add("modern-search-field");

            // Setup table styles
            gradesTableView.getStyleClass().add("modern-table");

            System.out.println("✅ Modern UI styles applied");
        } catch (Exception e) {
            System.err.println("⚠️ Error applying modern UI styles: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupCurrentTeacher() {
        try {
            User currentUser = SceneManager.getCurrentUser();
            if (currentUser instanceof Teacher) {
                this.currentTeacher = (Teacher) currentUser;
                System.out.println("✅ Current teacher set: " + currentTeacher.getTeacherId());
            } else {
                System.err.println("❌ Current user is not a teacher: " +
                    (currentUser != null ? currentUser.getClass().getSimpleName() : "null"));
                this.currentTeacher = null;
            }
        } catch (Exception e) {
            System.err.println("❌ Error setting up current teacher: " + e.getMessage());
            e.printStackTrace();
            this.currentTeacher = null;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentTeacher != null) {
            welcomeLabel.setText("Xin chào, " + currentTeacher.getFullName());
        }
    }
    
    private void setupTableColumns() {
        studentIdColumn.setCellValueFactory(new PropertyValueFactory<>("studentId"));

        // Safe student name column with null handling
        studentNameColumn.setCellValueFactory(cellData -> {
            try {
                Grade grade = cellData.getValue();
                if (grade == null || grade.getStudentId() == null) {
                    return new SimpleStringProperty("N/A");
                }

                User student = userService.getUserById(grade.getStudentId());
                String studentName = student != null && student.getFullName() != null ?
                        student.getFullName() : "Unknown Student";
                return new SimpleStringProperty(studentName);
            } catch (Exception e) {
                System.err.println("⚠️ Error getting student name: " + e.getMessage());
                return new SimpleStringProperty("Error");
            }
        });

        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        // Enhanced score column with color coding
        scoreColumn.setCellValueFactory(new PropertyValueFactory<>("score"));
        scoreColumn.setCellFactory(column -> new TableCell<Grade, Double>() {
            @Override
            protected void updateItem(Double score, boolean empty) {
                super.updateItem(score, empty);
                if (empty || score == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(String.format("%.1f", score));

                    // Color coding based on score
                    if (score >= 8.0) {
                        setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold; -fx-background-color: rgba(40, 167, 69, 0.1);");
                    } else if (score >= 6.5) {
                        setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold; -fx-background-color: rgba(255, 193, 7, 0.1);");
                    } else if (score >= 5.0) {
                        setStyle("-fx-text-fill: #fd7e14; -fx-font-weight: bold; -fx-background-color: rgba(253, 126, 20, 0.1);");
                    } else {
                        setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold; -fx-background-color: rgba(220, 53, 69, 0.1);");
                    }
                }
            }
        });

        // Enhanced letter grade column with styling
        letterGradeColumn.setCellValueFactory(new PropertyValueFactory<>("letterGrade"));
        letterGradeColumn.setCellFactory(column -> new TableCell<Grade, String>() {
            @Override
            protected void updateItem(String letterGrade, boolean empty) {
                super.updateItem(letterGrade, empty);
                if (empty || letterGrade == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(letterGrade);

                    // Style based on letter grade
                    switch (letterGrade) {
                        case "A+":
                        case "A":
                            setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold; -fx-font-size: 13px;");
                            break;
                        case "B+":
                        case "B":
                            setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold; -fx-font-size: 13px;");
                            break;
                        case "C+":
                        case "C":
                            setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold; -fx-font-size: 13px;");
                            break;
                        case "D":
                            setStyle("-fx-text-fill: #fd7e14; -fx-font-weight: bold; -fx-font-size: 13px;");
                            break;
                        case "F":
                            setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold; -fx-font-size: 13px;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #6c757d; -fx-font-weight: bold; -fx-font-size: 13px;");
                    }
                }
            }
        });

        semesterColumn.setCellValueFactory(new PropertyValueFactory<>("semester"));
        dateColumn.setCellValueFactory(new PropertyValueFactory<>("dateRecorded"));
        
        // Student name column
        studentNameColumn.setCellValueFactory(cellData -> {
            String studentId = cellData.getValue().getStudentId();
            User student = userService.getUserById(studentId);
            return new SimpleStringProperty(student != null ? student.getFullName() : "N/A");
        });
        
        // Actions column with buttons
        actionsColumn.setCellFactory(param -> new TableCell<Grade, Void>() {
            private final Button editBtn = new Button("Sửa");
            private final Button deleteBtn = new Button("Xóa");
            private final HBox buttons = new HBox(5, editBtn, deleteBtn);
            
            {
                editBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                deleteBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-font-size: 10px;");
                
                editBtn.setOnAction(e -> {
                    Grade grade = getTableView().getItems().get(getIndex());
                    showEditGradeDialog(grade);
                });
                
                deleteBtn.setOnAction(e -> {
                    Grade grade = getTableView().getItems().get(getIndex());
                    deleteGrade(grade);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttons);
                }
            }
        });
        
        gradesTableView.setItems(filteredGrades);
    }
    
    private void setupFilters() {
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterGrades());
        courseFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterGrades());
        semesterFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterGrades());
        
        // Setup semester filter
        semesterFilterComboBox.setItems(FXCollections.observableArrayList("2024-1", "2024-2", "2023-1", "2023-2"));
    }
    
    private void loadGrades() {
        try {
            if (currentTeacher == null) {
                System.err.println("⚠️ Current teacher is null, cannot load grades");
                return;
            }

            System.out.println("📊 Loading grades for teacher: " + currentTeacher.getTeacherId());

            // Load only grades for courses taught by this teacher
            List<Grade> teacherGrades = gradeService.getGradesByTeacher(currentTeacher.getTeacherId());

            // Filter out null grades
            if (teacherGrades != null) {
                teacherGrades = teacherGrades.stream()
                        .filter(grade -> grade != null)
                        .collect(Collectors.toList());
                allGrades.setAll(teacherGrades);
                System.out.println("✅ Loaded " + teacherGrades.size() + " grades");
            } else {
                allGrades.clear();
                System.out.println("⚠️ No grades found for teacher");
            }

            updateFilters();
            filterGrades();
            updateStatistics();

        } catch (Exception e) {
            System.err.println("❌ Error loading grades: " + e.getMessage());
            e.printStackTrace();
            allGrades.clear();
            filteredGrades.clear();
        }
    }
    
    private void updateFilters() {
        try {
            if (currentTeacher == null) {
                System.err.println("⚠️ Current teacher is null, cannot update filters");
                return;
            }

            // Update course filter with teacher's courses
            List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());

            if (teacherCourses != null) {
                List<String> courseNames = teacherCourses.stream()
                        .filter(course -> course != null && course.getCourseName() != null)
                        .map(Course::getCourseName)
                        .collect(Collectors.toList());
                courseFilterComboBox.setItems(FXCollections.observableArrayList(courseNames));
                System.out.println("✅ Updated course filter with " + courseNames.size() + " courses");
            } else {
                courseFilterComboBox.setItems(FXCollections.observableArrayList());
                System.out.println("⚠️ No courses found for teacher");
            }

        } catch (Exception e) {
            System.err.println("❌ Error updating filters: " + e.getMessage());
            e.printStackTrace();
            // Set empty list as fallback
            courseFilterComboBox.setItems(FXCollections.observableArrayList());
        }
    }
    
    private void filterGrades() {
        try {
            String searchText = searchField.getText() != null ? searchField.getText().toLowerCase() : "";
            String selectedCourse = courseFilterComboBox.getValue();
            String selectedSemester = semesterFilterComboBox.getValue();

            List<Grade> filtered = allGrades.stream()
                    .filter(grade -> {
                        if (grade == null) return false;

                        try {
                            // Get student name for search - with null safety
                            User student = userService.getUserById(grade.getStudentId());
                            String studentName = student != null && student.getFullName() != null ?
                                    student.getFullName().toLowerCase() : "";

                            // Safe search matching
                            String studentId = grade.getStudentId() != null ? grade.getStudentId().toLowerCase() : "";
                            boolean matchesSearch = searchText.isEmpty() ||
                                    studentName.contains(searchText) ||
                                    studentId.contains(searchText);

                            // Safe course matching
                            String courseName = grade.getCourseName() != null ? grade.getCourseName() : "";
                            boolean matchesCourse = selectedCourse == null ||
                                    courseName.equals(selectedCourse);

                            // Safe semester matching
                            String semester = grade.getSemester() != null ? grade.getSemester() : "";
                            boolean matchesSemester = selectedSemester == null ||
                                    semester.equals(selectedSemester);

                            return matchesSearch && matchesCourse && matchesSemester;
                        } catch (Exception e) {
                            System.err.println("⚠️ Error filtering grade: " + e.getMessage());
                            return false; // Skip problematic grades
                        }
                    })
                    .collect(Collectors.toList());

            filteredGrades.setAll(filtered);
            updateStatistics();

        } catch (Exception e) {
            System.err.println("❌ Error in filterGrades: " + e.getMessage());
            e.printStackTrace();
            // Fallback: show all grades
            filteredGrades.setAll(allGrades);
        }
    }
    
    private void updateStatistics() {
        try {
            if (filteredGrades == null) {
                System.err.println("⚠️ Filtered grades is null");
                return;
            }

            int totalCount = filteredGrades.size();

            // Check if we're using simple or modern layout
            String labelText = totalGradesLabel.getText();
            if (labelText != null && labelText.contains("Tổng số điểm:")) {
            // Simple layout format
            totalGradesLabel.setText("Tổng số điểm: " + totalCount);

            if (!filteredGrades.isEmpty()) {
                double average = filteredGrades.stream()
                        .mapToDouble(Grade::getScore)
                        .average()
                        .orElse(0.0);
                averageGradeLabel.setText(String.format("Điểm trung bình: %.2f", average));

                // Add visual feedback based on average
                if (average >= 8.0) {
                    averageGradeLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;"); // Green for excellent
                } else if (average >= 6.5) {
                    averageGradeLabel.setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold;"); // Yellow for good
                } else if (average >= 5.0) {
                    averageGradeLabel.setStyle("-fx-text-fill: #fd7e14; -fx-font-weight: bold;"); // Orange for average
                } else {
                    averageGradeLabel.setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold;"); // Red for poor
                }
            } else {
                averageGradeLabel.setText("Điểm trung bình: 0.00");
                averageGradeLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-weight: bold;"); // Gray for no data
            }

            // Update total count color based on quantity
            if (totalCount > 50) {
                totalGradesLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;"); // Green for many
            } else if (totalCount > 20) {
                totalGradesLabel.setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;"); // Blue for moderate
            } else if (totalCount > 0) {
                totalGradesLabel.setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold;"); // Yellow for few
            } else {
                totalGradesLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-weight: bold;"); // Gray for none
            }
        } else {
            // Modern layout format (just numbers)
            totalGradesLabel.setText(String.valueOf(totalCount));

            if (!filteredGrades.isEmpty()) {
                double average = filteredGrades.stream()
                        .mapToDouble(Grade::getScore)
                        .average()
                        .orElse(0.0);
                averageGradeLabel.setText(String.format("%.2f", average));

                // Add visual feedback based on average
                if (average >= 8.0) {
                    averageGradeLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;"); // Green for excellent
                } else if (average >= 6.5) {
                    averageGradeLabel.setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold;"); // Yellow for good
                } else if (average >= 5.0) {
                    averageGradeLabel.setStyle("-fx-text-fill: #fd7e14; -fx-font-weight: bold;"); // Orange for average
                } else {
                    averageGradeLabel.setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold;"); // Red for poor
                }
            } else {
                averageGradeLabel.setText("0.00");
                averageGradeLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-weight: bold;"); // Gray for no data
            }

            // Update total count color based on quantity
            if (totalCount > 50) {
                totalGradesLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;"); // Green for many
            } else if (totalCount > 20) {
                totalGradesLabel.setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;"); // Blue for moderate
            } else if (totalCount > 0) {
                totalGradesLabel.setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold;"); // Yellow for few
            } else {
                totalGradesLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-weight: bold;"); // Gray for none
            }
        }

        } catch (Exception e) {
            System.err.println("❌ Error updating statistics: " + e.getMessage());
            e.printStackTrace();
            // Fallback values
            try {
                totalGradesLabel.setText("0");
                averageGradeLabel.setText("0.00");
            } catch (Exception fallbackError) {
                System.err.println("❌ Even fallback failed: " + fallbackError.getMessage());
            }
        }
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void showAddGradeDialog() {
        showModernGradeDialog(null);
    }

    private void showModernGradeDialog(Grade existingGrade) {
        try {
            System.out.println("🎨 Attempting to load modern grade dialog...");

            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(
                getClass().getResource("/com/example/doancuoikyjava/grade-entry-dialog.fxml"));

            if (loader.getLocation() == null) {
                System.err.println("❌ FXML file not found, using fallback dialog");
                showOldGradeDialog(existingGrade);
                return;
            }

            javafx.scene.Parent root = loader.load();
            System.out.println("✅ FXML loaded successfully");

            // Apply CSS safely
            try {
                String cssPath = getClass().getResource("/com/example/doancuoikyjava/styles/grade-dialog.css").toExternalForm();
                root.getStylesheets().add(cssPath);
                System.out.println("✅ CSS applied successfully");
            } catch (Exception cssError) {
                System.err.println("⚠️ Could not load CSS, continuing without styling: " + cssError.getMessage());
            }

            GradeEntryDialogController controller = loader.getController();
            controller.setExistingGrade(existingGrade);
            controller.setCurrentTeacher(currentTeacher);

            javafx.stage.Stage dialogStage = new javafx.stage.Stage();
            dialogStage.setTitle(existingGrade == null ? "Nhập điểm mới" : "Sửa điểm");
            dialogStage.setScene(new javafx.scene.Scene(root));
            dialogStage.setResizable(false);
            dialogStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            dialogStage.initOwner(addGradeBtn.getScene().getWindow());

            System.out.println("🎭 Showing modern dialog...");
            dialogStage.showAndWait();

            Grade result = controller.getResult();
            if (result != null) {
                boolean success;
                if (existingGrade == null) {
                    success = gradeService.addGrade(result);
                    if (success) {
                        showAlert("Thành công", "Nhập điểm thành công!");
                    } else {
                        showAlert("Lỗi", "Không thể nhập điểm. Điểm có thể đã tồn tại cho sinh viên này trong học kỳ này.");
                    }
                } else {
                    success = gradeService.updateGrade(result);
                    if (success) {
                        showAlert("Thành công", "Cập nhật điểm thành công!");
                    } else {
                        showAlert("Lỗi", "Không thể cập nhật điểm.");
                    }
                }

                if (success) {
                    loadGrades();
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error showing modern grade dialog: " + e.getMessage());
            e.printStackTrace();
            System.out.println("🔄 Falling back to old dialog...");
            // Fallback to old dialog
            showOldGradeDialog(existingGrade);
        }
    }

    private void showOldGradeDialog(Grade existingGrade) {
        Dialog<Grade> dialog = createGradeDialog(existingGrade);
        Optional<Grade> result = dialog.showAndWait();

        result.ifPresent(grade -> {
            boolean success;
            if (existingGrade == null) {
                success = gradeService.addGrade(grade);
                if (success) {
                    showAlert("Thành công", "Nhập điểm thành công!");
                } else {
                    showAlert("Lỗi", "Không thể nhập điểm. Điểm có thể đã tồn tại cho sinh viên này trong học kỳ này.");
                }
            } else {
                success = gradeService.updateGrade(grade);
                if (success) {
                    showAlert("Thành công", "Cập nhật điểm thành công!");
                } else {
                    showAlert("Lỗi", "Không thể cập nhật điểm.");
                }
            }

            if (success) {
                loadGrades();
            }
        });
    }
    
    private void showEditGradeDialog(Grade grade) {
        showModernGradeDialog(grade);
    }
    
    private Dialog<Grade> createGradeDialog(Grade existingGrade) {
        Dialog<Grade> dialog = new Dialog<>();
        dialog.setTitle(existingGrade == null ? "Nhập điểm mới" : "Sửa điểm");
        dialog.setHeaderText(null);
        
        ButtonType saveButtonType = new ButtonType("Lưu", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);
        
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));
        
        ComboBox<String> studentComboBox = new ComboBox<>();
        ComboBox<String> courseComboBox = new ComboBox<>();
        TextField scoreField = new TextField();
        ComboBox<String> semesterComboBox = new ComboBox<>();
        
        // Load students enrolled in teacher's courses
        List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
        List<String> allStudentIds = teacherCourses.stream()
                .flatMap(course -> course.getEnrolledStudents().stream())
                .distinct()
                .collect(Collectors.toList());
        
        List<String> studentOptions = allStudentIds.stream()
                .map(id -> {
                    User student = userService.getUserById(id);
                    return student != null ? id + " - " + student.getFullName() : id;
                })
                .collect(Collectors.toList());
        studentComboBox.setItems(FXCollections.observableArrayList(studentOptions));
        
        // Load teacher's courses
        List<String> courseOptions = teacherCourses.stream()
                .map(c -> c.getCourseId() + " - " + c.getCourseName())
                .collect(Collectors.toList());
        courseComboBox.setItems(FXCollections.observableArrayList(courseOptions));
        
        // Setup semester options
        semesterComboBox.setItems(FXCollections.observableArrayList("2024-1", "2024-2", "2023-1", "2023-2"));
        
        if (existingGrade != null) {
            // Find and select student
            User student = userService.getUserById(existingGrade.getStudentId());
            String studentOption = existingGrade.getStudentId() + " - " + 
                    (student != null ? student.getFullName() : "N/A");
            studentComboBox.setValue(studentOption);
            studentComboBox.setDisable(true);
            
            // Find and select course
            String courseOption = existingGrade.getCourseId() + " - " + existingGrade.getCourseName();
            courseComboBox.setValue(courseOption);
            courseComboBox.setDisable(true);
            
            scoreField.setText(String.valueOf(existingGrade.getScore()));
            semesterComboBox.setValue(existingGrade.getSemester());
            semesterComboBox.setDisable(true);
        }
        
        grid.add(new Label("Sinh viên:"), 0, 0);
        grid.add(studentComboBox, 1, 0);
        grid.add(new Label("Môn học:"), 0, 1);
        grid.add(courseComboBox, 1, 1);
        grid.add(new Label("Điểm số (0-10):"), 0, 2);
        grid.add(scoreField, 1, 2);
        grid.add(new Label("Học kỳ:"), 0, 3);
        grid.add(semesterComboBox, 1, 3);
        
        dialog.getDialogPane().setContent(grid);
        
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                try {
                    Grade grade = existingGrade != null ? existingGrade : new Grade();
                    
                    if (existingGrade == null) {
                        // Parse student ID
                        String studentOption = studentComboBox.getValue();
                        String studentId = studentOption.split(" - ")[0];
                        
                        // Parse course ID and get course info
                        String courseOption = courseComboBox.getValue();
                        String courseId = courseOption.split(" - ")[0];
                        Course course = courseService.getCourseById(courseId);
                        
                        grade.setStudentId(studentId);
                        grade.setCourseId(courseId);
                        grade.setCourseName(course.getCourseName());
                        grade.setCredits(course.getCredits());
                        grade.setSemester(semesterComboBox.getValue());
                        grade.setTeacherId(currentTeacher.getTeacherId());
                    }
                    
                    // Validate and set score (0-10 scale)
                    double score = Double.parseDouble(scoreField.getText());
                    if (score < 0 || score > 10) {
                        showAlert("Lỗi", "Điểm số phải từ 0 đến 10");
                        return null;
                    }
                    grade.setScore(score);

                    return grade;
                } catch (Exception e) {
                    showAlert("Lỗi", "Dữ liệu không hợp lệ: " + e.getMessage());
                    return null;
                }
            }
            return null;
        });
        
        return dialog;
    }
    
    private void deleteGrade(Grade grade) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa điểm này?");
        
        User student = userService.getUserById(grade.getStudentId());
        String studentName = student != null ? student.getFullName() : "N/A";
        alert.setContentText("Sinh viên: " + studentName + "\nMôn học: " + grade.getCourseName() + 
                           "\nĐiểm: " + grade.getScore());
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            if (gradeService.deleteGrade(grade.getGradeId())) {
                showAlert("Thành công", "Xóa điểm thành công!");
                loadGrades();
            } else {
                showAlert("Lỗi", "Không thể xóa điểm.");
            }
        }
    }
    
    @FXML
    private void refreshData() {
        loadGrades();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchGrades() {
        filterGrades();
    }
    
    @FXML
    private void exportGrades() {
        showAlert("Thông báo", "Tính năng xuất điểm đang được phát triển!");
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}

package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;

public class TeacherGradesControllerSafe implements Initializable {
    
    @FXML private Button backButton;
    @FXML private Label welcomeLabel;
    @FXML private Button addGradeBtn;
    @FXML private Button refreshBtn;
    @FXML private ComboBox<String> courseFilterComboBox;
    @FXML private ComboBox<String> semesterFilterComboBox;
    @FXML private TextField searchField;
    @FXML private Button searchBtn;
    @FXML private TableView<Grade> gradesTableView;
    @FXML private TableColumn<Grade, String> studentIdColumn;
    @FXML private TableColumn<Grade, String> studentNameColumn;
    @FXML private TableColumn<Grade, String> courseNameColumn;
    @FXML private TableColumn<Grade, Integer> creditsColumn;
    @FXML private TableColumn<Grade, Double> scoreColumn;
    @FXML private TableColumn<Grade, String> letterGradeColumn;
    @FXML private TableColumn<Grade, String> semesterColumn;
    @FXML private TableColumn<Grade, LocalDate> dateColumn;
    @FXML private TableColumn<Grade, Void> actionsColumn;
    @FXML private Label totalGradesLabel;
    @FXML private Label averageGradeLabel;
    @FXML private Button exportBtn;
    
    private GradeService gradeService;
    private UserService userService;
    private CourseService courseService;
    private Teacher currentTeacher;
    private ObservableList<Grade> allGrades;
    private ObservableList<Grade> filteredGrades;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🎯 Initializing SAFE TeacherGradesController...");
        
        try {
            // Initialize services
            gradeService = new GradeService();
            userService = new UserService();
            courseService = new CourseService();
            
            // Initialize collections
            allGrades = FXCollections.observableArrayList();
            filteredGrades = FXCollections.observableArrayList();
            
            // Setup current teacher
            setupCurrentTeacher();
            
            // Setup welcome message
            setupWelcomeMessage();
            
            // Setup table columns (simple version)
            setupSimpleTableColumns();
            
            // Setup filters
            setupSimpleFilters();
            
            // Load grades safely
            loadGradesSafely();
            
            System.out.println("🎉 SAFE TeacherGradesController initialized successfully!");
            
        } catch (Exception e) {
            System.err.println("❌ Error in safe initialization: " + e.getMessage());
            e.printStackTrace();
            
            // Ensure basic functionality
            if (allGrades == null) allGrades = FXCollections.observableArrayList();
            if (filteredGrades == null) filteredGrades = FXCollections.observableArrayList();
            if (gradesTableView != null) gradesTableView.setItems(filteredGrades);
        }
    }
    
    private void setupCurrentTeacher() {
        try {
            User currentUser = SceneManager.getCurrentUser();
            if (currentUser instanceof Teacher) {
                this.currentTeacher = (Teacher) currentUser;
                System.out.println("✅ Current teacher: " + currentTeacher.getTeacherId());
            } else {
                System.err.println("❌ Current user is not a teacher");
                this.currentTeacher = null;
            }
        } catch (Exception e) {
            System.err.println("❌ Error setting up teacher: " + e.getMessage());
            this.currentTeacher = null;
        }
    }
    
    private void setupWelcomeMessage() {
        try {
            if (welcomeLabel != null && currentTeacher != null) {
                welcomeLabel.setText("Xin chào, " + currentTeacher.getFullName());
            }
        } catch (Exception e) {
            System.err.println("❌ Error setting welcome message: " + e.getMessage());
        }
    }
    
    private void setupSimpleTableColumns() {
        try {
            // Simple property value factories - no custom cell factories
            studentIdColumn.setCellValueFactory(new PropertyValueFactory<>("studentId"));
            studentNameColumn.setCellValueFactory(cellData -> {
                try {
                    Grade grade = cellData.getValue();
                    if (grade != null && grade.getStudentId() != null) {
                        User student = userService.getUserById(grade.getStudentId());
                        String name = (student != null && student.getFullName() != null) ? 
                                student.getFullName() : "Unknown";
                        return new javafx.beans.property.SimpleStringProperty(name);
                    }
                    return new javafx.beans.property.SimpleStringProperty("N/A");
                } catch (Exception e) {
                    return new javafx.beans.property.SimpleStringProperty("Error");
                }
            });
            
            courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
            creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
            scoreColumn.setCellValueFactory(new PropertyValueFactory<>("score"));
            letterGradeColumn.setCellValueFactory(new PropertyValueFactory<>("letterGrade"));
            semesterColumn.setCellValueFactory(new PropertyValueFactory<>("semester"));
            dateColumn.setCellValueFactory(new PropertyValueFactory<>("dateRecorded"));
            
            // Disable sorting to prevent comparison issues
            for (TableColumn<Grade, ?> column : gradesTableView.getColumns()) {
                column.setSortable(false);
            }
            
            gradesTableView.setItems(filteredGrades);
            
            System.out.println("✅ Simple table columns setup completed");
            
        } catch (Exception e) {
            System.err.println("❌ Error setting up table columns: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupSimpleFilters() {
        try {
            // Setup semester filter
            if (semesterFilterComboBox != null) {
                semesterFilterComboBox.setItems(FXCollections.observableArrayList(
                    "2024-1", "2024-2", "2025-1", "2025-2"));
            }
            
            System.out.println("✅ Simple filters setup completed");
            
        } catch (Exception e) {
            System.err.println("❌ Error setting up filters: " + e.getMessage());
        }
    }
    
    private void loadGradesSafely() {
        try {
            if (currentTeacher == null) {
                System.err.println("⚠️ No current teacher, cannot load grades");
                updateStatisticsSafely();
                return;
            }
            
            System.out.println("📊 Loading grades for teacher: " + currentTeacher.getTeacherId());
            
            List<Grade> teacherGrades = gradeService.getGradesByTeacher(currentTeacher.getTeacherId());
            
            if (teacherGrades != null) {
                // Filter out any problematic grades
                List<Grade> validGrades = new ArrayList<>();
                for (Grade grade : teacherGrades) {
                    if (isValidGrade(grade)) {
                        validGrades.add(grade);
                    } else {
                        System.out.println("⚠️ Skipping invalid grade");
                    }
                }
                
                allGrades.setAll(validGrades);
                filteredGrades.setAll(validGrades);
                
                System.out.println("✅ Loaded " + validGrades.size() + " valid grades");
            } else {
                allGrades.clear();
                filteredGrades.clear();
                System.out.println("⚠️ No grades found");
            }
            
            updateStatisticsSafely();
            
        } catch (Exception e) {
            System.err.println("❌ Error loading grades: " + e.getMessage());
            e.printStackTrace();
            
            // Ensure collections are not null
            if (allGrades == null) allGrades = FXCollections.observableArrayList();
            if (filteredGrades == null) filteredGrades = FXCollections.observableArrayList();
            
            allGrades.clear();
            filteredGrades.clear();
            updateStatisticsSafely();
        }
    }
    
    private boolean isValidGrade(Grade grade) {
        return grade != null &&
               grade.getStudentId() != null &&
               grade.getCourseId() != null &&
               grade.getCourseName() != null &&
               grade.getSemester() != null &&
               grade.getTeacherId() != null;
    }
    
    private void updateStatisticsSafely() {
        try {
            if (totalGradesLabel != null && averageGradeLabel != null && filteredGrades != null) {
                int count = filteredGrades.size();
                totalGradesLabel.setText("Tổng số điểm: " + count);
                
                if (count > 0) {
                    double average = filteredGrades.stream()
                            .filter(grade -> grade != null)
                            .mapToDouble(Grade::getScore)
                            .average()
                            .orElse(0.0);
                    averageGradeLabel.setText(String.format("Điểm trung bình: %.2f", average));
                } else {
                    averageGradeLabel.setText("Điểm trung bình: 0.00");
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error updating statistics: " + e.getMessage());
            try {
                if (totalGradesLabel != null) totalGradesLabel.setText("Tổng số điểm: 0");
                if (averageGradeLabel != null) averageGradeLabel.setText("Điểm trung bình: 0.00");
            } catch (Exception fallback) {
                System.err.println("❌ Even fallback statistics failed");
            }
        }
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại: " + e.getMessage());
        }
    }
    
    @FXML
    private void showAddGradeDialog() {
        showAlert("Thông báo", "Tính năng nhập điểm đang được phát triển!");
    }
    
    @FXML
    private void refreshData() {
        loadGradesSafely();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchGrades() {
        // Simple search - just reload for now
        loadGradesSafely();
    }
    
    @FXML
    private void exportGrades() {
        showAlert("Thông báo", "Tính năng xuất điểm đang được phát triển!");
    }
    
    private void showAlert(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("❌ Error showing alert: " + e.getMessage());
        }
    }
}

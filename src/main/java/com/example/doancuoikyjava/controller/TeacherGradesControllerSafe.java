package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Dialog;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

public class TeacherGradesControllerSafe implements Initializable {
    
    @FXML private Button backButton;
    @FXML private Label welcomeLabel;
    @FXML private Button addGradeBtn;
    @FXML private Button refreshBtn;
    @FXML private ComboBox<String> courseFilterComboBox;
    @FXML private ComboBox<String> semesterFilterComboBox;
    @FXML private TextField searchField;
    @FXML private Button searchBtn;
    @FXML private TableView<Grade> gradesTableView;
    @FXML private TableColumn<Grade, String> studentIdColumn;
    @FXML private TableColumn<Grade, String> studentNameColumn;
    @FXML private TableColumn<Grade, String> courseNameColumn;
    @FXML private TableColumn<Grade, Integer> creditsColumn;
    @FXML private TableColumn<Grade, Double> scoreColumn;
    @FXML private TableColumn<Grade, String> letterGradeColumn;
    @FXML private TableColumn<Grade, String> semesterColumn;
    @FXML private TableColumn<Grade, LocalDate> dateColumn;
    @FXML private TableColumn<Grade, Void> actionsColumn;
    @FXML private Label totalGradesLabel;
    @FXML private Label averageGradeLabel;
    @FXML private Button exportBtn;
    
    private GradeService gradeService;
    private UserService userService;
    private CourseService courseService;
    private Teacher currentTeacher;
    private ObservableList<Grade> allGrades;
    private ObservableList<Grade> filteredGrades;

    // Dialog form fields
    private ComboBox<String> dialogStudentComboBox;
    private ComboBox<String> dialogCourseComboBox;
    private TextField dialogScoreField;
    private ComboBox<String> dialogSemesterComboBox;
    private Label scorePreviewLabel;
    private Label letterGradePreviewLabel;
    private VBox scorePreviewContainer;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🎯 Initializing SAFE TeacherGradesController...");
        
        try {
            // Initialize services
            gradeService = new GradeService();
            userService = new UserService();
            courseService = new CourseService();
            
            // Initialize collections
            allGrades = FXCollections.observableArrayList();
            filteredGrades = FXCollections.observableArrayList();
            
            // Setup current teacher
            setupCurrentTeacher();
            
            // Setup welcome message
            setupWelcomeMessage();
            
            // Setup table columns (simple version)
            setupSimpleTableColumns();
            
            // Setup filters
            setupSimpleFilters();
            
            // Load grades safely
            loadGradesSafely();
            
            System.out.println("🎉 SAFE TeacherGradesController initialized successfully!");
            
        } catch (Exception e) {
            System.err.println("❌ Error in safe initialization: " + e.getMessage());
            e.printStackTrace();
            
            // Ensure basic functionality
            if (allGrades == null) allGrades = FXCollections.observableArrayList();
            if (filteredGrades == null) filteredGrades = FXCollections.observableArrayList();
            if (gradesTableView != null) gradesTableView.setItems(filteredGrades);
        }
    }
    
    private void setupCurrentTeacher() {
        try {
            User currentUser = SceneManager.getCurrentUser();
            if (currentUser instanceof Teacher) {
                this.currentTeacher = (Teacher) currentUser;
                System.out.println("✅ Current teacher: " + currentTeacher.getTeacherId());
            } else {
                System.err.println("❌ Current user is not a teacher");
                this.currentTeacher = null;
            }
        } catch (Exception e) {
            System.err.println("❌ Error setting up teacher: " + e.getMessage());
            this.currentTeacher = null;
        }
    }
    
    private void setupWelcomeMessage() {
        try {
            if (welcomeLabel != null && currentTeacher != null) {
                welcomeLabel.setText("Xin chào, " + currentTeacher.getFullName());
            }
        } catch (Exception e) {
            System.err.println("❌ Error setting welcome message: " + e.getMessage());
        }
    }
    
    private void setupSimpleTableColumns() {
        try {
            // Simple property value factories - no custom cell factories
            studentIdColumn.setCellValueFactory(new PropertyValueFactory<>("studentId"));
            studentNameColumn.setCellValueFactory(cellData -> {
                try {
                    Grade grade = cellData.getValue();
                    if (grade != null && grade.getStudentId() != null) {
                        User student = userService.getUserById(grade.getStudentId());
                        String name = (student != null && student.getFullName() != null) ? 
                                student.getFullName() : "Unknown";
                        return new javafx.beans.property.SimpleStringProperty(name);
                    }
                    return new javafx.beans.property.SimpleStringProperty("N/A");
                } catch (Exception e) {
                    return new javafx.beans.property.SimpleStringProperty("Error");
                }
            });
            
            courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
            creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
            scoreColumn.setCellValueFactory(new PropertyValueFactory<>("score"));
            letterGradeColumn.setCellValueFactory(new PropertyValueFactory<>("letterGrade"));
            semesterColumn.setCellValueFactory(new PropertyValueFactory<>("semester"));
            dateColumn.setCellValueFactory(new PropertyValueFactory<>("dateRecorded"));
            
            // Disable sorting to prevent comparison issues
            for (TableColumn<Grade, ?> column : gradesTableView.getColumns()) {
                column.setSortable(false);
            }
            
            gradesTableView.setItems(filteredGrades);
            
            System.out.println("✅ Simple table columns setup completed");
            
        } catch (Exception e) {
            System.err.println("❌ Error setting up table columns: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupSimpleFilters() {
        try {
            // Setup semester filter
            if (semesterFilterComboBox != null) {
                semesterFilterComboBox.setItems(FXCollections.observableArrayList(
                    "2024-1", "2024-2", "2025-1", "2025-2"));
            }
            
            System.out.println("✅ Simple filters setup completed");
            
        } catch (Exception e) {
            System.err.println("❌ Error setting up filters: " + e.getMessage());
        }
    }
    
    private void loadGradesSafely() {
        try {
            if (currentTeacher == null) {
                System.err.println("⚠️ No current teacher, cannot load grades");
                updateStatisticsSafely();
                return;
            }
            
            System.out.println("📊 Loading grades for teacher: " + currentTeacher.getTeacherId());
            
            List<Grade> teacherGrades = gradeService.getGradesByTeacher(currentTeacher.getTeacherId());
            
            if (teacherGrades != null) {
                // Filter out any problematic grades
                List<Grade> validGrades = new ArrayList<>();
                for (Grade grade : teacherGrades) {
                    if (isValidGrade(grade)) {
                        validGrades.add(grade);
                    } else {
                        System.out.println("⚠️ Skipping invalid grade");
                    }
                }
                
                allGrades.setAll(validGrades);
                filteredGrades.setAll(validGrades);
                
                System.out.println("✅ Loaded " + validGrades.size() + " valid grades");
            } else {
                allGrades.clear();
                filteredGrades.clear();
                System.out.println("⚠️ No grades found");
            }
            
            updateStatisticsSafely();
            
        } catch (Exception e) {
            System.err.println("❌ Error loading grades: " + e.getMessage());
            e.printStackTrace();
            
            // Ensure collections are not null
            if (allGrades == null) allGrades = FXCollections.observableArrayList();
            if (filteredGrades == null) filteredGrades = FXCollections.observableArrayList();
            
            allGrades.clear();
            filteredGrades.clear();
            updateStatisticsSafely();
        }
    }
    
    private boolean isValidGrade(Grade grade) {
        return grade != null &&
               grade.getStudentId() != null &&
               grade.getCourseId() != null &&
               grade.getCourseName() != null &&
               grade.getSemester() != null &&
               grade.getTeacherId() != null;
    }
    
    private void updateStatisticsSafely() {
        try {
            if (totalGradesLabel != null && averageGradeLabel != null && filteredGrades != null) {
                int count = filteredGrades.size();
                totalGradesLabel.setText("Tổng số điểm: " + count);
                
                if (count > 0) {
                    double average = filteredGrades.stream()
                            .filter(grade -> grade != null)
                            .mapToDouble(Grade::getScore)
                            .average()
                            .orElse(0.0);
                    averageGradeLabel.setText(String.format("Điểm trung bình: %.2f", average));
                } else {
                    averageGradeLabel.setText("Điểm trung bình: 0.00");
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error updating statistics: " + e.getMessage());
            try {
                if (totalGradesLabel != null) totalGradesLabel.setText("Tổng số điểm: 0");
                if (averageGradeLabel != null) averageGradeLabel.setText("Điểm trung bình: 0.00");
            } catch (Exception fallback) {
                System.err.println("❌ Even fallback statistics failed");
            }
        }
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại: " + e.getMessage());
        }
    }
    
    @FXML
    private void showAddGradeDialog() {
        try {
            System.out.println("🎨 Opening modern grade entry dialog...");

            Dialog<Grade> dialog = createModernGradeDialog();
            Optional<Grade> result = dialog.showAndWait();

            result.ifPresent(grade -> {
                try {
                    boolean success = gradeService.addGrade(grade);
                    if (success) {
                        showAlert("Thành công", "✅ Nhập điểm thành công!\n\n" +
                                "Sinh viên: " + getStudentName(grade.getStudentId()) + "\n" +
                                "Môn học: " + grade.getCourseName() + "\n" +
                                "Điểm số: " + grade.getScore() + "/10\n" +
                                "Xếp loại: " + grade.getLetterGrade());
                        loadGradesSafely();
                    } else {
                        showAlert("Lỗi", "❌ Không thể nhập điểm!\n\nCó thể do:\n" +
                                "• Điểm đã tồn tại cho sinh viên này\n" +
                                "• Lỗi kết nối cơ sở dữ liệu\n" +
                                "• Dữ liệu không hợp lệ");
                    }
                } catch (Exception e) {
                    System.err.println("❌ Error saving grade: " + e.getMessage());
                    showAlert("Lỗi", "❌ Lỗi khi lưu điểm: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            System.err.println("❌ Error showing grade dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "❌ Không thể mở dialog nhập điểm: " + e.getMessage());
        }
    }

    private String getStudentName(String studentId) {
        try {
            User student = userService.getUserById(studentId);
            return student != null && student.getFullName() != null ?
                    student.getFullName() : "Unknown Student";
        } catch (Exception e) {
            return "Unknown Student";
        }
    }
    
    @FXML
    private void refreshData() {
        loadGradesSafely();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchGrades() {
        // Simple search - just reload for now
        loadGradesSafely();
    }
    
    @FXML
    private void exportGrades() {
        showAlert("Thông báo", "Tính năng xuất điểm đang được phát triển!");
    }
    
    private Dialog<Grade> createModernGradeDialog() {
        Dialog<Grade> dialog = new Dialog<>();
        dialog.setTitle("🎓 Nhập điểm mới - Thang điểm 0-10");
        dialog.setHeaderText(null);

        // Create custom buttons
        ButtonType saveButtonType = new ButtonType("💾 Lưu điểm", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("❌ Hủy bỏ", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, cancelButtonType);

        // Create main container - extra small
        VBox mainContainer = new VBox();
        mainContainer.setSpacing(0);
        mainContainer.setPrefWidth(400);
        mainContainer.setPrefHeight(450);
        mainContainer.setStyle("-fx-background-color: #f8f9fa; -fx-background-radius: 10px;");

        // Create modern header
        VBox headerContainer = createModernHeader();

        // Create form container
        VBox formContainer = createModernForm();

        // Add components
        mainContainer.getChildren().addAll(headerContainer, formContainer);

        // Set dialog content - extra small
        dialog.getDialogPane().setContent(mainContainer);
        dialog.getDialogPane().setPrefSize(440, 500);
        dialog.setResizable(false);

        // Style the dialog
        try {
            dialog.getDialogPane().getStylesheets().add(
                getClass().getResource("/com/example/doancuoikyjava/styles/grade-dialog.css").toExternalForm());
        } catch (Exception e) {
            System.err.println("⚠️ Could not load dialog CSS: " + e.getMessage());
        }

        // Setup result converter
        setupDialogResultConverter(dialog, saveButtonType);

        // Style buttons
        styleDialogButtons(dialog, saveButtonType, cancelButtonType);

        return dialog;
    }

    private VBox createModernHeader() {
        VBox headerContainer = new VBox();
        headerContainer.setSpacing(0);
        headerContainer.setStyle(
            "-fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);" +
            "-fx-background-radius: 16px 16px 0 0;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 3);"
        );

        // Main header content - extra compact
        HBox headerContent = new HBox();
        headerContent.setSpacing(8);
        headerContent.setAlignment(Pos.CENTER_LEFT);
        headerContent.setPadding(new Insets(12, 20, 12, 20));

        // Icon container
        VBox iconContainer = new VBox();
        iconContainer.setAlignment(Pos.CENTER);
        iconContainer.setStyle(
            "-fx-background-color: rgba(255,255,255,0.2);" +
            "-fx-background-radius: 30px;" +
            "-fx-padding: 6px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 1);"
        );

        Label iconLabel = new Label("🎓");
        iconLabel.setStyle(
            "-fx-font-size: 18px;" +
            "-fx-text-fill: white;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.5), 1, 0, 0, 1);"
        );
        iconContainer.getChildren().add(iconLabel);

        // Title section
        VBox titleSection = new VBox();
        titleSection.setSpacing(5);

        Label titleLabel = new Label("NHẬP ĐIỂM");
        titleLabel.setStyle(
            "-fx-font-size: 14px;" +
            "-fx-font-weight: bold;" +
            "-fx-text-fill: white;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 1, 0, 0, 1);"
        );

        Label subtitleLabel = new Label("0-10 điểm");
        subtitleLabel.setStyle(
            "-fx-font-size: 10px;" +
            "-fx-text-fill: rgba(255,255,255,0.9);" +
            "-fx-font-style: italic;"
        );

        titleSection.getChildren().addAll(titleLabel, subtitleLabel);

        headerContent.getChildren().addAll(iconContainer, titleSection);
        headerContainer.getChildren().add(headerContent);

        return headerContainer;
    }

    private VBox createModernForm() {
        VBox formContainer = new VBox();
        formContainer.setSpacing(10);
        formContainer.setPadding(new Insets(15, 20, 15, 20));
        formContainer.setStyle("-fx-background-color: linear-gradient(to bottom, #f8f9fa, #ffffff);");

        // Student selection card
        VBox studentCard = createInputCard("👨‍🎓", "Sinh viên", "Chọn sinh viên cần nhập điểm");
        dialogStudentComboBox = createModernComboBox("🔍 Tìm và chọn sinh viên...");
        loadStudentsForDialog();
        studentCard.getChildren().add(dialogStudentComboBox);

        // Course selection card
        VBox courseCard = createInputCard("📚", "Môn học", "Chọn môn học cần nhập điểm");
        dialogCourseComboBox = createModernComboBox("🔍 Chọn môn học...");
        loadCoursesForDialog();
        courseCard.getChildren().add(dialogCourseComboBox);

        // Score input card (special highlight)
        VBox scoreCard = createScoreInputCard();

        // Semester selection card
        VBox semesterCard = createInputCard("📅", "Học kỳ", "Chọn học kỳ");
        dialogSemesterComboBox = createModernComboBox("🔍 Chọn học kỳ...");
        dialogSemesterComboBox.setItems(FXCollections.observableArrayList(
            "2024-1", "2024-2", "2025-1", "2025-2"));
        semesterCard.getChildren().add(dialogSemesterComboBox);

        formContainer.getChildren().addAll(studentCard, courseCard, scoreCard, semesterCard);

        return formContainer;
    }

    private VBox createInputCard(String icon, String title, String description) {
        VBox card = new VBox();
        card.setSpacing(6);
        card.setStyle(
            "-fx-background-color: white;" +
            "-fx-background-radius: 8px;" +
            "-fx-padding: 10px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 1);" +
            "-fx-border-color: rgba(102, 126, 234, 0.1);" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 8px;"
        );

        // Header
        HBox header = new HBox();
        header.setSpacing(10);
        header.setAlignment(Pos.CENTER_LEFT);

        Label iconLabel = new Label(icon);
        iconLabel.setStyle(
            "-fx-background-color: linear-gradient(135deg, #667eea, #764ba2);" +
            "-fx-background-radius: 12px;" +
            "-fx-padding: 4px;" +
            "-fx-text-fill: white;" +
            "-fx-font-size: 12px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);"
        );

        VBox titleSection = new VBox();
        titleSection.setSpacing(3);

        Label titleLabel = new Label(title);
        titleLabel.setStyle(
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;" +
            "-fx-text-fill: #2c3e50;"
        );

        Label descLabel = new Label(description);
        descLabel.setStyle(
            "-fx-font-size: 9px;" +
            "-fx-text-fill: #6c757d;" +
            "-fx-font-style: italic;"
        );

        titleSection.getChildren().addAll(titleLabel, descLabel);
        header.getChildren().addAll(iconLabel, titleSection);
        card.getChildren().add(header);

        return card;
    }

    private VBox createScoreInputCard() {
        VBox scoreCard = new VBox();
        scoreCard.setSpacing(6);
        scoreCard.setStyle(
            "-fx-background-color: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);" +
            "-fx-background-radius: 8px;" +
            "-fx-padding: 12px;" +
            "-fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.2), 6, 0, 0, 2);" +
            "-fx-border-color: linear-gradient(135deg, #667eea, #764ba2);" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 8px;"
        );

        // Header
        HBox header = new HBox();
        header.setSpacing(12);
        header.setAlignment(Pos.CENTER_LEFT);

        Label scoreIcon = new Label("⭐");
        scoreIcon.setStyle(
            "-fx-background-color: linear-gradient(135deg, #ffd700, #ffb347);" +
            "-fx-background-radius: 15px;" +
            "-fx-padding: 5px;" +
            "-fx-text-fill: white;" +
            "-fx-font-size: 12px;" +
            "-fx-effect: dropshadow(gaussian, rgba(255, 215, 0, 0.4), 4, 0, 0, 1);"
        );

        VBox titleSection = new VBox();
        titleSection.setSpacing(3);

        Label titleLabel = new Label("Điểm số");
        titleLabel.setStyle(
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;" +
            "-fx-text-fill: #2c3e50;"
        );

        Label hintLabel = new Label("0-10");
        hintLabel.setStyle(
            "-fx-font-size: 9px;" +
            "-fx-text-fill: #6c757d;" +
            "-fx-font-style: italic;"
        );

        titleSection.getChildren().addAll(titleLabel, hintLabel);
        header.getChildren().addAll(scoreIcon, titleSection);

        // Score input section
        HBox scoreInputSection = new HBox();
        scoreInputSection.setSpacing(15);
        scoreInputSection.setAlignment(Pos.CENTER_LEFT);

        dialogScoreField = new TextField();
        dialogScoreField.setPromptText("8.5");
        dialogScoreField.setPrefWidth(120);
        dialogScoreField.setStyle(
            "-fx-background-color: white;" +
            "-fx-border-color: #e1e8ed;" +
            "-fx-border-radius: 6px;" +
            "-fx-background-radius: 6px;" +
            "-fx-padding: 8px 12px;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-min-height: 32px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 1);" +
            "-fx-text-fill: #2c3e50;"
        );

        Label maxScoreLabel = new Label("/10");
        maxScoreLabel.setStyle(
            "-fx-text-fill: #667eea;" +
            "-fx-background-color: rgba(102, 126, 234, 0.1);" +
            "-fx-background-radius: 8px;" +
            "-fx-padding: 8px 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-font-size: 16px;"
        );

        scoreInputSection.getChildren().addAll(dialogScoreField, maxScoreLabel);

        // Score preview section
        createScorePreview();

        scoreCard.getChildren().addAll(header, scoreInputSection, scorePreviewContainer);

        // Add real-time score validation and preview
        setupScoreValidation();

        return scoreCard;
    }

    private ComboBox<String> createModernComboBox(String promptText) {
        ComboBox<String> comboBox = new ComboBox<>();
        comboBox.setPromptText(promptText);
        comboBox.setMaxWidth(Double.MAX_VALUE);
        comboBox.setStyle(
            "-fx-background-color: white;" +
            "-fx-border-color: #e1e8ed;" +
            "-fx-border-radius: 6px;" +
            "-fx-background-radius: 6px;" +
            "-fx-padding: 8px 10px;" +
            "-fx-font-size: 11px;" +
            "-fx-min-height: 32px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);"
        );
        return comboBox;
    }

    private void createScorePreview() {
        scorePreviewContainer = new VBox();
        scorePreviewContainer.setSpacing(4);
        scorePreviewContainer.setVisible(false);
        scorePreviewContainer.setStyle(
            "-fx-background-color: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 100%);" +
            "-fx-border-color: linear-gradient(135deg, #28a745, #20c997);" +
            "-fx-border-radius: 6px;" +
            "-fx-background-radius: 6px;" +
            "-fx-padding: 8px;" +
            "-fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.2), 4, 0, 0, 1);"
        );

        // Separator
        Region separator = new Region();
        separator.setStyle("-fx-background-color: rgba(40, 167, 69, 0.3); -fx-pref-height: 2px;");

        // Preview content
        HBox previewContent = new HBox();
        previewContent.setSpacing(10);
        previewContent.setAlignment(Pos.CENTER);

        // Letter grade preview
        VBox letterGradeSection = new VBox();
        letterGradeSection.setSpacing(5);
        letterGradeSection.setAlignment(Pos.CENTER);
        letterGradeSection.setStyle(
            "-fx-background-color: rgba(255, 255, 255, 0.7);" +
            "-fx-background-radius: 4px;" +
            "-fx-padding: 6px 10px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);"
        );

        Label letterGradeTitle = new Label("XẾP LOẠI");
        letterGradeTitle.setStyle(
            "-fx-text-fill: #495057;" +
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;"
        );

        letterGradePreviewLabel = new Label("A+");
        letterGradePreviewLabel.setStyle(
            "-fx-text-fill: #28a745;" +
            "-fx-font-weight: bold;" +
            "-fx-font-size: 14px;" +
            "-fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.3), 1, 0, 0, 1);"
        );

        letterGradeSection.getChildren().addAll(letterGradeTitle, letterGradePreviewLabel);

        // Vertical separator
        Region verticalSeparator = new Region();
        verticalSeparator.setStyle(
            "-fx-background-color: rgba(40, 167, 69, 0.3);" +
            "-fx-pref-width: 2px;" +
            "-fx-min-height: 40px;"
        );

        // Score preview
        VBox scoreSection = new VBox();
        scoreSection.setSpacing(5);
        scoreSection.setAlignment(Pos.CENTER);
        scoreSection.setStyle(
            "-fx-background-color: rgba(255, 255, 255, 0.7);" +
            "-fx-background-radius: 4px;" +
            "-fx-padding: 6px 10px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);"
        );

        Label scoreTitle = new Label("ĐIỂM SỐ");
        scoreTitle.setStyle(
            "-fx-text-fill: #495057;" +
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;"
        );

        scorePreviewLabel = new Label("10.0");
        scorePreviewLabel.setStyle(
            "-fx-text-fill: #007bff;" +
            "-fx-font-weight: bold;" +
            "-fx-font-size: 14px;" +
            "-fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.3), 1, 0, 0, 1);"
        );

        scoreSection.getChildren().addAll(scoreTitle, scorePreviewLabel);

        previewContent.getChildren().addAll(letterGradeSection, verticalSeparator, scoreSection);
        scorePreviewContainer.getChildren().addAll(separator, previewContent);
    }

    private void setupScoreValidation() {
        dialogScoreField.textProperty().addListener((obs, oldText, newText) -> {
            try {
                if (newText == null || newText.trim().isEmpty()) {
                    scorePreviewContainer.setVisible(false);
                    return;
                }

                double score = Double.parseDouble(newText.trim());
                if (score >= 0 && score <= 10) {
                    // Valid score - show preview
                    scorePreviewLabel.setText(String.format("%.1f", score));
                    letterGradePreviewLabel.setText(calculateLetterGrade(score));
                    scorePreviewContainer.setVisible(true);

                    // Update field style for valid input
                    dialogScoreField.setStyle(
                        "-fx-background-color: white;" +
                        "-fx-border-color: #28a745;" +
                        "-fx-border-radius: 10px;" +
                        "-fx-background-radius: 10px;" +
                        "-fx-padding: 15px 18px;" +
                        "-fx-font-size: 16px;" +
                        "-fx-font-weight: bold;" +
                        "-fx-min-height: 50px;" +
                        "-fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.3), 8, 0, 0, 0);" +
                        "-fx-text-fill: #2c3e50;"
                    );
                } else {
                    // Invalid range
                    scorePreviewContainer.setVisible(false);
                    dialogScoreField.setStyle(
                        "-fx-background-color: white;" +
                        "-fx-border-color: #dc3545;" +
                        "-fx-border-radius: 10px;" +
                        "-fx-background-radius: 10px;" +
                        "-fx-padding: 15px 18px;" +
                        "-fx-font-size: 16px;" +
                        "-fx-font-weight: bold;" +
                        "-fx-min-height: 50px;" +
                        "-fx-effect: dropshadow(gaussian, rgba(220, 53, 69, 0.3), 8, 0, 0, 0);" +
                        "-fx-text-fill: #2c3e50;"
                    );
                }
            } catch (NumberFormatException e) {
                // Invalid number format
                scorePreviewContainer.setVisible(false);
                if (!newText.trim().isEmpty()) {
                    dialogScoreField.setStyle(
                        "-fx-background-color: white;" +
                        "-fx-border-color: #dc3545;" +
                        "-fx-border-radius: 10px;" +
                        "-fx-background-radius: 10px;" +
                        "-fx-padding: 15px 18px;" +
                        "-fx-font-size: 16px;" +
                        "-fx-font-weight: bold;" +
                        "-fx-min-height: 50px;" +
                        "-fx-effect: dropshadow(gaussian, rgba(220, 53, 69, 0.3), 8, 0, 0, 0);" +
                        "-fx-text-fill: #2c3e50;"
                    );
                }
            }
        });
    }

    private String calculateLetterGrade(double score) {
        if (score >= 9.0) return "A+";
        else if (score >= 8.5) return "A";
        else if (score >= 8.0) return "B+";
        else if (score >= 7.0) return "B";
        else if (score >= 6.5) return "C+";
        else if (score >= 5.5) return "C";
        else if (score >= 5.0) return "D";
        else return "F";
    }

    private void loadStudentsForDialog() {
        try {
            List<User> students = userService.getUsersByRole(User.UserRole.STUDENT);
            if (students != null) {
                List<String> studentOptions = students.stream()
                        .filter(user -> user != null && user.getFullName() != null && user.getUserId() != null)
                        .map(user -> user.getUserId() + " - " + user.getFullName())
                        .collect(java.util.stream.Collectors.toList());
                dialogStudentComboBox.setItems(FXCollections.observableArrayList(studentOptions));
            }
        } catch (Exception e) {
            System.err.println("❌ Error loading students for dialog: " + e.getMessage());
        }
    }

    private void loadCoursesForDialog() {
        try {
            if (currentTeacher != null) {
                List<Course> courses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
                if (courses != null) {
                    List<String> courseOptions = courses.stream()
                            .filter(course -> course != null && course.getCourseName() != null && course.getCourseId() != null)
                            .map(course -> course.getCourseId() + " - " + course.getCourseName())
                            .collect(java.util.stream.Collectors.toList());
                    dialogCourseComboBox.setItems(FXCollections.observableArrayList(courseOptions));
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error loading courses for dialog: " + e.getMessage());
        }
    }

    private void setupDialogResultConverter(Dialog<Grade> dialog, ButtonType saveButtonType) {
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                try {
                    // Validate inputs
                    if (dialogStudentComboBox.getValue() == null) {
                        showAlert("Lỗi", "❌ Vui lòng chọn sinh viên!");
                        return null;
                    }

                    if (dialogCourseComboBox.getValue() == null) {
                        showAlert("Lỗi", "❌ Vui lòng chọn môn học!");
                        return null;
                    }

                    if (dialogScoreField.getText() == null || dialogScoreField.getText().trim().isEmpty()) {
                        showAlert("Lỗi", "❌ Vui lòng nhập điểm số!");
                        return null;
                    }

                    if (dialogSemesterComboBox.getValue() == null) {
                        showAlert("Lỗi", "❌ Vui lòng chọn học kỳ!");
                        return null;
                    }

                    // Parse and validate score
                    double score;
                    try {
                        score = Double.parseDouble(dialogScoreField.getText().trim());
                        if (score < 0 || score > 10) {
                            showAlert("Lỗi", "❌ Điểm số phải từ 0 đến 10!");
                            return null;
                        }
                    } catch (NumberFormatException e) {
                        showAlert("Lỗi", "❌ Điểm số không hợp lệ!");
                        return null;
                    }

                    // Create grade object
                    Grade grade = new Grade();

                    // Parse student ID
                    String studentOption = dialogStudentComboBox.getValue();
                    String studentId = studentOption.split(" - ")[0];
                    grade.setStudentId(studentId);

                    // Parse course info
                    String courseOption = dialogCourseComboBox.getValue();
                    String courseId = courseOption.split(" - ")[0];
                    Course course = courseService.getCourseById(courseId);

                    grade.setCourseId(courseId);
                    grade.setCourseName(course != null ? course.getCourseName() : "Unknown Course");
                    grade.setCredits(course != null ? course.getCredits() : 3);
                    grade.setSemester(dialogSemesterComboBox.getValue());
                    grade.setTeacherId(currentTeacher != null ? currentTeacher.getTeacherId() : "Unknown");

                    // Set score (this will automatically calculate letter grade)
                    grade.setScore(score);

                    return grade;

                } catch (Exception e) {
                    System.err.println("❌ Error creating grade: " + e.getMessage());
                    showAlert("Lỗi", "❌ Lỗi khi tạo điểm: " + e.getMessage());
                    return null;
                }
            }
            return null;
        });
    }

    private void styleDialogButtons(Dialog<Grade> dialog, ButtonType saveButtonType, ButtonType cancelButtonType) {
        javafx.application.Platform.runLater(() -> {
            try {
                Button saveBtn = (Button) dialog.getDialogPane().lookupButton(saveButtonType);
                Button cancelBtn = (Button) dialog.getDialogPane().lookupButton(cancelButtonType);

                if (saveBtn != null) {
                    saveBtn.setStyle(
                        "-fx-background-color: linear-gradient(135deg, #667eea, #764ba2);" +
                        "-fx-text-fill: white;" +
                        "-fx-font-weight: bold;" +
                        "-fx-font-size: 10px;" +
                        "-fx-border-radius: 15px;" +
                        "-fx-background-radius: 15px;" +
                        "-fx-padding: 8px 20px;" +
                        "-fx-cursor: hand;" +
                        "-fx-min-width: 100px;" +
                        "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 1);"
                    );
                }

                if (cancelBtn != null) {
                    cancelBtn.setStyle(
                        "-fx-background-color: linear-gradient(135deg, #6c757d, #5a6268);" +
                        "-fx-text-fill: white;" +
                        "-fx-font-weight: bold;" +
                        "-fx-font-size: 10px;" +
                        "-fx-border-radius: 15px;" +
                        "-fx-background-radius: 15px;" +
                        "-fx-padding: 8px 20px;" +
                        "-fx-cursor: hand;" +
                        "-fx-min-width: 100px;" +
                        "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 1);"
                    );
                }
            } catch (Exception e) {
                System.err.println("❌ Error styling dialog buttons: " + e.getMessage());
            }
        });
    }

    private void showAlert(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("❌ Error showing alert: " + e.getMessage());
        }
    }
}

package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.ChatService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.io.IOException;
import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

public class TeacherColleagueChatController implements Initializable {
    
    @FXML private Button backButton;
    @FXML private Label welcomeLabel;
    @FXML private TextField searchField;
    @FXML private ListView<Teacher> teacherListView;
    @FXML private Button refreshButton;
    @FXML private Label chatHeaderLabel;
    @FXML private Label onlineStatusLabel;
    @FXML private ScrollPane chatScrollPane;
    @FXML private VBox chatArea;
    @FXML private TextField messageField;
    @FXML private Button sendButton;
    
    private ChatService chatService;
    private User currentUser;
    private Teacher selectedTeacher;
    private List<ChatMessage> currentChatHistory;
    private Timer refreshTimer;
    private ObservableList<Teacher> allTeachers;
    private ObservableList<Teacher> filteredTeachers;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            System.out.println("🎯 Initializing TeacherColleagueChatController...");
            
            chatService = new ChatService();
            currentUser = SceneManager.getCurrentUser();
            currentChatHistory = new ArrayList<>();
            allTeachers = FXCollections.observableArrayList();
            filteredTeachers = FXCollections.observableArrayList();
            
            if (currentUser == null) {
                showAlert("Lỗi", "Không tìm thấy thông tin người dùng!");
                return;
            }
            
            setupWelcomeMessage();
            setupTeacherListView();
            setupSearchField();
            loadAvailableTeachers();
            startAutoRefresh();
            
            System.out.println("✅ TeacherColleagueChatController initialized successfully!");
            
        } catch (Exception e) {
            System.err.println("❌ Error initializing TeacherColleagueChatController: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể khởi tạo chat: " + e.getMessage());
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentUser != null) {
            welcomeLabel.setText("Xin chào, " + currentUser.getFullName());
        }
    }
    
    private void setupTeacherListView() {
        teacherListView.setItems(filteredTeachers);
        
        // Custom cell factory for teacher display
        teacherListView.setCellFactory(listView -> new ListCell<Teacher>() {
            @Override
            protected void updateItem(Teacher teacher, boolean empty) {
                super.updateItem(teacher, empty);
                
                if (empty || teacher == null) {
                    setText(null);
                    setGraphic(null);
                } else {
                    VBox container = new VBox(5);
                    container.setPadding(new Insets(8, 12, 8, 12));
                    
                    // Teacher name
                    Label nameLabel = new Label(teacher.getFullName());
                    nameLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
                    nameLabel.setStyle("-fx-text-fill: #2c3e50;");
                    
                    // Teacher info
                    String info = "👨‍🏫 " + teacher.getTeacherId();
                    if (teacher.getDepartment() != null && !teacher.getDepartment().isEmpty()) {
                        info += " • " + teacher.getDepartment();
                    }
                    
                    Label infoLabel = new Label(info);
                    infoLabel.setFont(Font.font("System", 11));
                    infoLabel.setStyle("-fx-text-fill: #6c757d;");
                    
                    container.getChildren().addAll(nameLabel, infoLabel);
                    setGraphic(container);
                    setText(null);
                    
                    // Style for selection
                    setStyle("-fx-background-color: transparent; -fx-border-color: transparent;");
                    
                    // Hover effect
                    setOnMouseEntered(e -> {
                        if (!isSelected()) {
                            setStyle("-fx-background-color: #f8f9fa; -fx-border-color: transparent;");
                        }
                    });
                    
                    setOnMouseExited(e -> {
                        if (!isSelected()) {
                            setStyle("-fx-background-color: transparent; -fx-border-color: transparent;");
                        }
                    });
                }
            }
        });
        
        // Handle teacher selection
        teacherListView.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                selectTeacher(newSelection);
            }
        });
    }
    
    private void setupSearchField() {
        searchField.textProperty().addListener((obs, oldText, newText) -> {
            filterTeachers(newText);
        });
    }
    
    private void filterTeachers(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            filteredTeachers.setAll(allTeachers);
        } else {
            String lowerCaseFilter = searchText.toLowerCase().trim();
            List<Teacher> filtered = allTeachers.stream()
                    .filter(teacher -> teacher != null &&
                            ((teacher.getFullName() != null && teacher.getFullName().toLowerCase().contains(lowerCaseFilter)) ||
                             (teacher.getTeacherId() != null && teacher.getTeacherId().toLowerCase().contains(lowerCaseFilter)) ||
                             (teacher.getDepartment() != null && teacher.getDepartment().toLowerCase().contains(lowerCaseFilter))))
                    .collect(java.util.stream.Collectors.toList());
            filteredTeachers.setAll(filtered);
        }
    }
    
    private void loadAvailableTeachers() {
        try {
            System.out.println("📚 Loading available colleague teachers...");
            
            if (!(currentUser instanceof Teacher)) {
                System.err.println("❌ Current user is not a teacher");
                return;
            }
            
            Teacher currentTeacher = (Teacher) currentUser;
            List<Teacher> teachers = chatService.getAvailableTeachersForTeacher(currentTeacher.getTeacherId());

            // Filter out null teachers and add null checks
            List<Teacher> validTeachers = new ArrayList<>();
            for (Teacher teacher : teachers) {
                if (teacher != null && teacher.getFullName() != null && !teacher.getFullName().trim().isEmpty()) {
                    validTeachers.add(teacher);
                }
            }

            Platform.runLater(() -> {
                allTeachers.setAll(validTeachers);
                filteredTeachers.setAll(validTeachers);

                if (validTeachers.isEmpty()) {
                    // Show message if no teachers available
                    Label noTeachersLabel = new Label("Không có đồng nghiệp nào để chat.\nChưa có giáo viên khác dạy cùng môn.");
                    noTeachersLabel.setStyle("-fx-text-fill: gray; -fx-font-style: italic; -fx-text-alignment: center;");
                    teacherListView.setPlaceholder(noTeachersLabel);
                } else {
                    Label placeholderLabel = new Label("Chọn đồng nghiệp để bắt đầu chat");
                    placeholderLabel.setStyle("-fx-text-fill: gray; -fx-font-style: italic;");
                    teacherListView.setPlaceholder(placeholderLabel);
                }
            });

            System.out.println("👨‍🏫 Loaded " + validTeachers.size() + " valid colleague teachers (filtered from " + teachers.size() + " total)");

        } catch (Exception e) {
            System.err.println("❌ Error loading colleague teachers: " + e.getMessage());
            e.printStackTrace();

            // Show error message to user
            Platform.runLater(() -> {
                Label errorLabel = new Label("❌ Lỗi tải danh sách đồng nghiệp.\nVui lòng thử lại sau.");
                errorLabel.setStyle("-fx-text-fill: red; -fx-font-style: italic; -fx-text-alignment: center;");
                teacherListView.setPlaceholder(errorLabel);
            });
        }
    }
    
    private void selectTeacher(Teacher teacher) {
        selectedTeacher = teacher;
        
        // Update UI
        chatHeaderLabel.setText("Chat với " + teacher.getFullName());
        messageField.setDisable(false);
        sendButton.setDisable(false);
        
        // Check online status (simplified - always show as available)
        updateOnlineStatus(teacher.getUserId());
        
        // Load chat history
        loadChatHistory();
        
        System.out.println("👨‍🏫 Selected colleague teacher: " + teacher.getFullName());
    }
    
    private void updateOnlineStatus(String userId) {
        // Simplified online status - always show as available for colleagues
        Platform.runLater(() -> {
            onlineStatusLabel.setText("🟢 Có thể chat");
            onlineStatusLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
        });
    }
    
    private void loadChatHistory() {
        if (selectedTeacher == null) return;
        
        try {
            currentChatHistory = chatService.getChatHistory(currentUser.getUserId(), selectedTeacher.getUserId());
            
            Platform.runLater(() -> {
                chatArea.getChildren().clear();
                
                for (ChatMessage message : currentChatHistory) {
                    addMessageToChat(message);
                }
                
                // Scroll to bottom
                Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
            });
            
            System.out.println("📜 Loaded " + currentChatHistory.size() + " messages with colleague");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading chat history: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void addMessageToChat(ChatMessage message) {
        try {
            HBox messageContainer = new HBox();
            messageContainer.setPadding(new Insets(5, 10, 5, 10));
            
            VBox messageBox = new VBox(3);
            messageBox.setMaxWidth(400);
            
            // Message content
            Label messageLabel = new Label(message.getContent());
            messageLabel.setWrapText(true);
            messageLabel.setPadding(new Insets(10, 15, 10, 15));
            messageLabel.setFont(Font.font("System", 13));
            
            // Timestamp
            Label timeLabel = new Label(message.getTimestamp().format(DateTimeFormatter.ofPattern("HH:mm dd/MM")));
            timeLabel.setFont(Font.font("System", 10));
            timeLabel.setStyle("-fx-text-fill: #6c757d;");
            
            boolean isMyMessage = message.getSenderId().equals(currentUser.getUserId());
            
            if (isMyMessage) {
                // My message (right side)
                messageLabel.setStyle(
                    "-fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);" +
                    "-fx-text-fill: white;" +
                    "-fx-background-radius: 18px 18px 5px 18px;" +
                    "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);"
                );
                
                messageBox.getChildren().addAll(messageLabel, timeLabel);
                messageBox.setAlignment(Pos.CENTER_RIGHT);
                messageContainer.getChildren().add(messageBox);
                messageContainer.setAlignment(Pos.CENTER_RIGHT);
                
            } else {
                // Their message (left side)
                messageLabel.setStyle(
                    "-fx-background-color: #f1f3f4;" +
                    "-fx-text-fill: #2c3e50;" +
                    "-fx-background-radius: 18px 18px 18px 5px;" +
                    "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);"
                );
                
                messageBox.getChildren().addAll(messageLabel, timeLabel);
                messageBox.setAlignment(Pos.CENTER_LEFT);
                messageContainer.getChildren().add(messageBox);
                messageContainer.setAlignment(Pos.CENTER_LEFT);
            }
            
            chatArea.getChildren().add(messageContainer);
            
        } catch (Exception e) {
            System.err.println("❌ Error adding message to chat: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @FXML
    private void sendMessage() {
        if (selectedTeacher == null || messageField.getText().trim().isEmpty()) {
            return;
        }
        
        try {
            String messageContent = messageField.getText().trim();
            
            // Create and send message
            ChatMessage message = new ChatMessage();
            message.setSenderId(currentUser.getUserId());
            message.setReceiverId(selectedTeacher.getUserId());
            message.setContent(messageContent);
            message.setTimestamp(java.time.LocalDateTime.now());
            message.setMessageId(java.util.UUID.randomUUID().toString());
            
            boolean success = chatService.sendMessage(message);
            
            if (success) {
                // Add to current chat history
                currentChatHistory.add(message);
                
                // Add to UI
                Platform.runLater(() -> {
                    addMessageToChat(message);
                    messageField.clear();
                    
                    // Scroll to bottom
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
                
                System.out.println("📤 Message sent to colleague: " + selectedTeacher.getFullName());
                
            } else {
                showAlert("Lỗi", "Không thể gửi tin nhắn. Vui lòng thử lại.");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error sending message: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Lỗi khi gửi tin nhắn: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.ENTER) {
            sendMessage();
        }
    }
    
    @FXML
    private void refreshTeacherList() {
        loadAvailableTeachers();
        showAlert("Thông báo", "Đã làm mới danh sách đồng nghiệp!");
    }
    
    private void startAutoRefresh() {
        refreshTimer = new Timer(true);
        refreshTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                refreshChatHistory();
            }
        }, 5000, 5000); // Refresh every 5 seconds
    }
    
    private void refreshChatHistory() {
        if (selectedTeacher == null) return;
        
        try {
            List<ChatMessage> latestHistory = chatService.getChatHistory(currentUser.getUserId(), selectedTeacher.getUserId());
            
            // Check if there are new messages
            if (latestHistory.size() > currentChatHistory.size()) {
                Platform.runLater(() -> {
                    // Clear and reload all messages
                    chatArea.getChildren().clear();
                    currentChatHistory = latestHistory;
                    
                    for (ChatMessage message : currentChatHistory) {
                        addMessageToChat(message);
                    }
                    
                    // Scroll to bottom
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
                
                System.out.println("🔄 Colleague chat refreshed - " + latestHistory.size() + " messages");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error refreshing colleague chat: " + e.getMessage());
        }
    }
    
    @FXML
    private void goBack() {
        try {
            if (refreshTimer != null) {
                refreshTimer.cancel();
            }
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + currentUser.getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại: " + e.getMessage());
        }
    }
    
    private void showAlert(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("❌ Error showing alert: " + e.getMessage());
        }
    }
}

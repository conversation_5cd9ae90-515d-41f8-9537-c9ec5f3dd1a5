package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.CourseService;
import com.example.doancuoikyjava.service.ExcelExportHelper;
import com.example.doancuoikyjava.service.GradeService;
import com.example.doancuoikyjava.service.ScheduleService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

public class TeacherController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button logoutButton;
    @FXML private Button myCoursesBtn;
    @FXML private Button studentsBtn;
    @FXML private Button gradesBtn;
    @FXML private Button scheduleBtn;
    @FXML private Button notificationsBtn;
    @FXML private Button chatBtn;
    @FXML private Button colleagueChatBtn;
    @FXML private Button exportBtn;
    @FXML private Button profileBtn;
    
    @FXML private Label totalCoursesLabel;
    @FXML private Label totalStudentsLabel;
    @FXML private Label pendingGradesLabel;
    
    @FXML private TableView<Course> coursesTableView;
    @FXML private TableColumn<Course, String> courseIdColumn;
    @FXML private TableColumn<Course, String> courseNameColumn;
    @FXML private TableColumn<Course, Integer> creditsColumn;
    @FXML private TableColumn<Course, Integer> studentsCountColumn;
    @FXML private TableColumn<Course, String> scheduleColumn;
    
    @FXML private ListView<String> todayScheduleListView;
    @FXML private ListView<String> notificationsListView;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private Teacher currentTeacher;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        
        setupCurrentTeacher();
        setupWelcomeMessage();
        setupTableColumns();
        loadDashboardData();
        setupScheduleAndNotifications();
    }
    
    private void setupCurrentTeacher() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Teacher) {
            currentTeacher = (Teacher) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentTeacher != null) {
            welcomeLabel.setText("Xin chào, " + currentTeacher.getFullName());
        }
    }
    
    private void setupTableColumns() {
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        scheduleColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(course));
        });
        
        // Custom cell factory for students count
        studentsCountColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            int count = course.getEnrolledStudents().size();
            return new javafx.beans.property.SimpleObjectProperty<>(count);
        });
    }
    
    private void loadDashboardData() {
        if (currentTeacher == null) return;
        
        // Load courses taught by this teacher
        List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
        
        // Update statistics
        totalCoursesLabel.setText(String.valueOf(teacherCourses.size()));
        
        int totalStudents = teacherCourses.stream()
                .mapToInt(course -> course.getEnrolledStudents().size())
                .sum();
        totalStudentsLabel.setText(String.valueOf(totalStudents));
        
        // For demo purposes
        pendingGradesLabel.setText("3");
        
        // Load courses into table
        ObservableList<Course> coursesList = FXCollections.observableArrayList(teacherCourses);
        coursesTableView.setItems(coursesList);
    }
    
    private void setupScheduleAndNotifications() {
        // Setup today's schedule
        ObservableList<String> todaySchedule = FXCollections.observableArrayList(
            "07:30 - 09:30: Lập trình Java - Phòng A101",
            "09:45 - 11:45: Cơ sở dữ liệu - Phòng A102",
            "13:30 - 15:30: Thực hành Java - Lab B201"
        );
        todayScheduleListView.setItems(todaySchedule);
        
        // Setup notifications
        ObservableList<String> notifications = FXCollections.observableArrayList(
            "📝 Cần nhập điểm cho môn Lập trình Java",
            "📚 Sinh viên mới đăng ký môn Cơ sở dữ liệu",
            "📅 Lịch dạy tuần tới đã được cập nhật",
            "💡 Nhắc nhở: Họp khoa vào thứ 6 lúc 14:00"
        );
        notificationsListView.setItems(notifications);
    }
    
    @FXML
    private void handleLogout() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận đăng xuất");
        alert.setHeaderText("Bạn có chắc chắn muốn đăng xuất?");
        alert.setContentText("Tất cả dữ liệu chưa lưu sẽ bị mất.");
        
        if (alert.showAndWait().get() == ButtonType.OK) {
            SceneManager.logout();
        }
    }
    
    @FXML
    private void showMyCourses() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-courses.fxml",
                                   "Môn học giảng dạy - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang môn học: " + e.getMessage());
        }
    }
    
    @FXML
    private void showStudents() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-students.fxml",
                                   "Danh sách sinh viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang danh sách sinh viên: " + e.getMessage());
        }
    }
    
    @FXML
    private void showGrades() {
        try {
            System.out.println("🎯 Opening Teacher Grades...");

            // Try simple version first (most stable)
            try {
                SceneManager.switchScene("/com/example/doancuoikyjava/teacher-grades-simple.fxml",
                                       "Quản lý điểm - " + SceneManager.getCurrentUser().getFullName());
                System.out.println("✅ Simple teacher grades loaded");
                return;
            } catch (Exception simpleError) {
                System.err.println("⚠️ Simple version failed: " + simpleError.getMessage());
            }

            // Try modern version as fallback
            try {
                SceneManager.switchScene("/com/example/doancuoikyjava/teacher-grades.fxml",
                                       "Quản lý điểm - " + SceneManager.getCurrentUser().getFullName());
                System.out.println("✅ Modern teacher grades loaded");
            } catch (Exception modernError) {
                System.err.println("⚠️ Modern version also failed: " + modernError.getMessage());
                throw modernError;
            }

        } catch (Exception e) {
            System.err.println("❌ All versions failed, showing error: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể mở trang quản lý điểm. Vui lòng kiểm tra dữ liệu và thử lại.\n\nLỗi: " + e.getMessage());
        }
    }
    
    @FXML
    private void showSchedule() {
        try {
            System.out.println("📅 TeacherController: Attempting to show schedule...");
            User currentUser = SceneManager.getCurrentUser();

            if (currentUser == null) {
                showAlert("Lỗi", "Không tìm thấy thông tin người dùng hiện tại!");
                return;
            }

            System.out.println("👤 Current user: " + currentUser.getFullName() + " (Role: " + currentUser.getRole() + ")");

            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-schedule-simple.fxml",
                                   "Lịch giảng dạy - " + currentUser.getFullName());

            System.out.println("✅ TeacherController: Successfully switched to schedule scene");
        } catch (Exception e) {
            System.err.println("❌ TeacherController: Error showing schedule: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể mở trang lịch giảng dạy: " + e.getMessage());
        }
    }
    
    @FXML
    private void showProfile() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/simple-profile.fxml",
                                   "Thông tin cá nhân - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang thông tin cá nhân: " + e.getMessage());
        }
    }

    @FXML
    private void showChat() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-chat.fxml", "Chat với Sinh viên");
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang chat: " + e.getMessage());
        }
    }

    @FXML
    private void showColleagueChat() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-colleague-chat.fxml", "Chat với Đồng nghiệp");
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang chat đồng nghiệp: " + e.getMessage());
        }
    }

    @FXML
    private void showExportOptions() {
        showTeacherExportDialog();
    }

    @FXML
    private void showNotifications() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/notification-view.fxml",
                                   "Thông báo - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang thông báo: " + e.getMessage());
        }
    }
    
    /**
     * Extract time information from course schedule
     */
    private String extractTimeFromSchedule(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's time range
                return schedules.get(0).getTimeRange();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = course.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("-")) {
                    String[] parts = schedule.split("-");
                    if (parts.length >= 2) {
                        // Look for time pattern like "7:30-9:30"
                        String timePart = parts[parts.length - 1].trim();
                        if (timePart.matches(".*\\d{1,2}:\\d{2}.*")) {
                            return timePart;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting time from schedule: " + e.getMessage());
        }

        return "Chưa xếp";
    }

    private void showTeacherExportDialog() {
        try {
            System.out.println("📊 Opening teacher Excel export dialog...");

            Alert exportDialog = new Alert(Alert.AlertType.CONFIRMATION);
            exportDialog.setTitle("📊 Xuất dữ liệu Excel");
            exportDialog.setHeaderText("Chọn loại dữ liệu muốn xuất:");

            // Create custom buttons for teacher
            ButtonType myGradesBtn = new ButtonType("📝 Điểm môn tôi dạy");
            ButtonType myCoursesBtn = new ButtonType("📚 Môn học của tôi");
            ButtonType myStudentsBtn = new ButtonType("👨‍🎓 Sinh viên của tôi");
            ButtonType cancelBtn = new ButtonType("❌ Hủy", ButtonBar.ButtonData.CANCEL_CLOSE);

            exportDialog.getButtonTypes().setAll(myGradesBtn, myCoursesBtn, myStudentsBtn, cancelBtn);

            exportDialog.showAndWait().ifPresent(response -> {
                ExcelExportHelper exportHelper = new ExcelExportHelper();
                String filePath = null;

                User currentUser = SceneManager.getCurrentUser();
                if (!(currentUser instanceof Teacher)) {
                    showAlert("Lỗi", "❌ Chỉ giáo viên mới có thể sử dụng tính năng này!");
                    return;
                }

                Teacher currentTeacher = (Teacher) currentUser;
                String teacherId = currentTeacher.getTeacherId();

                if (response == myGradesBtn) {
                    // Export grades for courses taught by this teacher
                    GradeService gradeService = new GradeService();
                    filePath = exportHelper.exportTeacherGrades(teacherId, gradeService.getAllGrades());

                } else if (response == myCoursesBtn) {
                    // Export courses taught by this teacher
                    CourseService courseService = new CourseService();
                    filePath = exportHelper.exportCourses(courseService.getCoursesByTeacher(teacherId));

                } else if (response == myStudentsBtn) {
                    // Export students enrolled in teacher's courses
                    showAlert("Thông báo", "🚧 Tính năng xuất danh sách sinh viên đang được phát triển!");
                    return;
                }

                if (filePath != null) {
                    showAlert("Thành công", "✅ Đã xuất dữ liệu thành công!\n\n" +
                            "📁 File đã được lưu tại:\n" + filePath + "\n\n" +
                            "💡 Bạn có thể mở file bằng Microsoft Excel hoặc LibreOffice Calc.");
                } else {
                    showAlert("Lỗi", "❌ Không thể xuất dữ liệu!\n\nVui lòng thử lại sau.");
                }
            });

        } catch (Exception e) {
            System.err.println("❌ Error showing teacher export dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "❌ Không thể mở dialog xuất Excel: " + e.getMessage());
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}

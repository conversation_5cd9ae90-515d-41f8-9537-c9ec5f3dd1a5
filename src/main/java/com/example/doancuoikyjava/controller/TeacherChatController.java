package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.SimpleChatService;
import com.example.doancuoikyjava.service.FileUploadService;
import com.example.doancuoikyjava.service.AudioRecordingService;
import com.example.doancuoikyjava.service.FileDownloadService;
import com.example.doancuoikyjava.service.AudioPlayerService;
import com.example.doancuoikyjava.service.AttachmentPreviewService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Stage;

import java.io.File;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Controller for teacher chat functionality
 */
public class TeacherChatController implements Initializable {
    
    @FXML private Button backButton;
    @FXML private ListView<Student> studentListView;
    @FXML private VBox chatArea;
    @FXML private ScrollPane chatScrollPane;
    @FXML private TextField messageField;
    @FXML private Button sendButton;
    @FXML private Label chatHeaderLabel;
    @FXML private Label onlineStatusLabel;

    // New UI elements for enhanced chat
    @FXML private Button attachImageButton;
    @FXML private Button attachFileButton;
    @FXML private Button recordAudioButton;
    @FXML private Label recordingStatusLabel;
    @FXML private VBox attachmentPreviewArea;
    
    private SimpleChatService chatService;
    private FileUploadService fileUploadService;
    private AudioRecordingService audioRecordingService;
    private FileDownloadService fileDownloadService;
    private AudioPlayerService audioPlayerService;
    private AttachmentPreviewService attachmentPreviewService;
    private User currentUser;
    private Student selectedStudent;
    private List<ChatMessage> currentChatHistory;
    private ScheduledExecutorService refreshService;

    // Attachment state
    private File pendingImageAttachment;
    private File pendingFileAttachment;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currentUser = SceneManager.getCurrentUser();
        if (currentUser == null) {
            System.err.println("❌ No current user in session!");
            showAlert("Lỗi", "Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.");
            return;
        }

        chatService = new SimpleChatService();
        fileUploadService = new FileUploadService();
        audioRecordingService = new AudioRecordingService();
        fileDownloadService = new FileDownloadService();
        audioPlayerService = new AudioPlayerService();
        attachmentPreviewService = new AttachmentPreviewService();
        currentChatHistory = new ArrayList<>();

        // Setup audio player listener
        setupAudioPlayerListener();

        setupUI();
        loadAvailableStudents();
        startRefreshService();

        System.out.println("💬 Teacher Chat Controller initialized for: " + currentUser.getFullName());
    }

    private void setupAudioPlayerListener() {
        audioPlayerService.setListener(new AudioPlayerService.AudioPlayerListener() {
            @Override
            public void onPlaybackStarted(String fileName) {
                Platform.runLater(() -> {
                    System.out.println("🎵 Started playing: " + fileName);
                });
            }

            @Override
            public void onPlaybackStopped(String fileName) {
                Platform.runLater(() -> {
                    System.out.println("⏸️ Stopped playing: " + fileName);
                });
            }

            @Override
            public void onPlaybackCompleted(String fileName) {
                Platform.runLater(() -> {
                    System.out.println("✅ Completed playing: " + fileName);
                });
            }

            @Override
            public void onPlaybackError(String fileName, String error) {
                Platform.runLater(() -> {
                    System.err.println("❌ Audio error: " + error);
                    showAlert("Lỗi Audio", error);
                });
            }
        });
    }
    
    private void setupUI() {
        // Setup student list
        studentListView.setCellFactory(listView -> new StudentListCell());
        studentListView.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    selectStudent(newSelection);
                }
            }
        );
        
        // Setup message field
        messageField.setOnAction(e -> sendMessage());
        sendButton.setOnAction(e -> sendMessage());

        // Setup new buttons
        if (attachImageButton != null) {
            attachImageButton.setOnAction(e -> attachImage());
            attachImageButton.setText("🖼️");
            attachImageButton.setTooltip(new Tooltip("Gửi ảnh"));
        }

        if (attachFileButton != null) {
            attachFileButton.setOnAction(e -> attachFile());
            attachFileButton.setText("📎");
            attachFileButton.setTooltip(new Tooltip("Gửi file"));
        }

        if (recordAudioButton != null) {
            recordAudioButton.setOnAction(e -> toggleAudioRecording());
            recordAudioButton.setText("🎤");
            recordAudioButton.setTooltip(new Tooltip("Ghi âm"));
        }

        if (recordingStatusLabel != null) {
            recordingStatusLabel.setText("");
            recordingStatusLabel.setVisible(false);
        }
        
        // Setup chat area
        chatArea.setSpacing(10);
        chatArea.setPadding(new Insets(10));
        chatScrollPane.setFitToWidth(true);
        chatScrollPane.vvalueProperty().bind(chatArea.heightProperty());
        
        // Initial state
        chatHeaderLabel.setText("Chọn sinh viên để bắt đầu chat");
        messageField.setDisable(true);
        sendButton.setDisable(true);
        onlineStatusLabel.setText("Offline");
        onlineStatusLabel.setTextFill(Color.GRAY);
    }
    
    private void loadAvailableStudents() {
        try {
            List<Student> students = chatService.getAvailableStudentsForTeacher(currentUser.getUserId());

            // Filter out null students and add null checks
            List<Student> validStudents = new ArrayList<>();
            for (Student student : students) {
                if (student != null && student.getFullName() != null && !student.getFullName().trim().isEmpty()) {
                    validStudents.add(student);
                }
            }

            Platform.runLater(() -> {
                studentListView.getItems().clear();
                studentListView.getItems().addAll(validStudents);

                if (validStudents.isEmpty()) {
                    // Show message if no students available
                    Label noStudentsLabel = new Label("Không có sinh viên nào để chat.\nChưa có sinh viên trong các môn bạn dạy.");
                    noStudentsLabel.setStyle("-fx-text-fill: gray; -fx-font-style: italic;");
                    studentListView.setPlaceholder(noStudentsLabel);
                }
            });

            System.out.println("👨‍🎓 Loaded " + validStudents.size() + " valid students (filtered from " + students.size() + " total)");

        } catch (Exception e) {
            System.err.println("❌ Error loading students: " + e.getMessage());
            e.printStackTrace();

            // Show error message to user
            Platform.runLater(() -> {
                Label errorLabel = new Label("❌ Lỗi tải danh sách sinh viên.\nVui lòng thử lại sau.");
                errorLabel.setStyle("-fx-text-fill: red; -fx-font-style: italic;");
                studentListView.setPlaceholder(errorLabel);
            });
        }
    }
    
    private void selectStudent(Student student) {
        selectedStudent = student;
        
        // Update UI
        chatHeaderLabel.setText("Chat với " + student.getFullName());
        messageField.setDisable(false);
        sendButton.setDisable(false);
        
        // Check online status
        updateOnlineStatus(student.getUserId());
        
        // Load chat history
        loadChatHistory();
        
        System.out.println("👨‍🎓 Selected student: " + student.getFullName());
    }
    
    private void loadChatHistory() {
        if (selectedStudent == null) return;
        
        try {
            currentChatHistory = chatService.getChatHistory(currentUser.getUserId(), selectedStudent.getUserId());
            
            Platform.runLater(() -> {
                chatArea.getChildren().clear();
                
                for (ChatMessage message : currentChatHistory) {
                    addMessageToChat(message);
                }
                
                // Scroll to bottom
                Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
            });
            
            System.out.println("📜 Loaded " + currentChatHistory.size() + " messages");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading chat history: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void addMessageToChat(ChatMessage message) {
        boolean isSentByMe = message.isSentByUser(currentUser.getUserId());

        // Create message bubble
        VBox messageBox = new VBox(5);
        messageBox.setPadding(new Insets(10));
        messageBox.setMaxWidth(300);

        Label contentLabel = null; // Declare here for text messages

        // Handle different message types
        if (message.isTextWithImageMessage() && message.hasFile()) {
            // Text with image combo
            if (message.hasTextContent()) {
                contentLabel = new Label(message.getContent());
                contentLabel.setWrapText(true);
                contentLabel.setFont(Font.font("System", 14));
                messageBox.getChildren().add(contentLabel);
            }
            addImageContent(messageBox, message);
        } else if (message.isTextWithFileMessage() && message.hasFile()) {
            // Text with file combo
            if (message.hasTextContent()) {
                contentLabel = new Label(message.getContent());
                contentLabel.setWrapText(true);
                contentLabel.setFont(Font.font("System", 14));
                messageBox.getChildren().add(contentLabel);
            }
            addFileContent(messageBox, message);
        } else if (message.isImageMessage() && message.hasFile()) {
            addImageContent(messageBox, message);
        } else if (message.isAudioMessage() && message.hasFile()) {
            addAudioContent(messageBox, message);
        } else if (message.isFileMessage() && message.hasFile()) {
            addFileContent(messageBox, message);
        } else {
            // Regular text message
            contentLabel = new Label(message.getContent());
            contentLabel.setWrapText(true);
            contentLabel.setFont(Font.font("System", 14));
            messageBox.getChildren().add(contentLabel);
        }

        // Timestamp
        Label timeLabel = new Label(message.getTimeOnly());
        timeLabel.setFont(Font.font("System", 10));
        timeLabel.setTextFill(Color.GRAY);

        messageBox.getChildren().add(timeLabel);

        // Style based on sender
        if (isSentByMe) {
            messageBox.setStyle("-fx-background-color: #28a745; -fx-background-radius: 15; -fx-padding: 10;");
            if (contentLabel != null) {
                contentLabel.setTextFill(Color.WHITE);
            }
            // Set text color for file/media content
            setTextColorForChildren(messageBox, Color.WHITE);
            messageBox.setAlignment(Pos.CENTER_RIGHT);

            HBox container = new HBox();
            container.setAlignment(Pos.CENTER_RIGHT);
            container.getChildren().add(messageBox);
            chatArea.getChildren().add(container);
        } else {
            messageBox.setStyle("-fx-background-color: #f1f1f1; -fx-background-radius: 15; -fx-padding: 10;");
            if (contentLabel != null) {
                contentLabel.setTextFill(Color.BLACK);
            }
            // Set text color for file/media content
            setTextColorForChildren(messageBox, Color.BLACK);
            messageBox.setAlignment(Pos.CENTER_LEFT);

            HBox container = new HBox();
            container.setAlignment(Pos.CENTER_LEFT);
            container.getChildren().add(messageBox);
            chatArea.getChildren().add(container);
        }
    }

    // Helper method to set text color for all labels in a container
    private void setTextColorForChildren(VBox container, Color color) {
        container.getChildren().forEach(node -> {
            if (node instanceof Label) {
                ((Label) node).setTextFill(color);
            } else if (node instanceof HBox) {
                ((HBox) node).getChildren().forEach(child -> {
                    if (child instanceof VBox) {
                        setTextColorForChildren((VBox) child, color);
                    } else if (child instanceof Label) {
                        ((Label) child).setTextFill(color);
                    }
                });
            }
        });
    }
    
    private void sendMessage() {
        if (selectedStudent == null) {
            return;
        }

        String content = messageField.getText().trim();
        boolean hasText = !content.isEmpty();
        boolean hasImageAttachment = pendingImageAttachment != null;
        boolean hasFileAttachment = pendingFileAttachment != null;

        // Must have either text or attachment
        if (!hasText && !hasImageAttachment && !hasFileAttachment) {
            return;
        }

        try {
            ChatMessage message = null;

            if (hasImageAttachment && hasText) {
                // Text with image combo
                message = sendTextWithImageMessage(content);
            } else if (hasFileAttachment && hasText) {
                // Text with file combo
                message = sendTextWithFileMessage(content);
            } else if (hasImageAttachment) {
                // Image only
                message = sendImageOnlyMessage();
            } else if (hasFileAttachment) {
                // File only
                message = sendFileOnlyMessage();
            } else {
                // Text only
                message = sendTextOnlyMessage(content);
            }

            if (message != null) {
                currentChatHistory.add(message);
                ChatMessage finalMessage = message;
                Platform.runLater(() -> {
                    addMessageToChat(finalMessage);
                    messageField.clear();
                    clearAllAttachments();
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
                System.out.println("💬 Message sent successfully");
            }

        } catch (Exception e) {
            System.err.println("❌ Error sending message: " + e.getMessage());
            showAlert("Lỗi", "Không thể gửi tin nhắn: " + e.getMessage());
        }
    }
    
    private void startRefreshService() {
        refreshService = Executors.newSingleThreadScheduledExecutor();
        
        // Refresh chat every 3 seconds to check for new messages
        refreshService.scheduleAtFixedRate(() -> {
            if (selectedStudent != null) {
                refreshChatHistory();
            }
        }, 3, 3, TimeUnit.SECONDS);
        
        System.out.println("🔄 Teacher chat refresh service started");
    }
    
    private void refreshChatHistory() {
        if (selectedStudent == null) return;
        
        try {
            List<ChatMessage> latestHistory = chatService.getChatHistory(currentUser.getUserId(), selectedStudent.getUserId());
            
            // Check if there are new messages
            if (latestHistory.size() > currentChatHistory.size()) {
                Platform.runLater(() -> {
                    // Clear and reload all messages
                    chatArea.getChildren().clear();
                    currentChatHistory = latestHistory;
                    
                    for (ChatMessage message : currentChatHistory) {
                        addMessageToChat(message);
                    }
                    
                    // Scroll to bottom
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
                
                System.out.println("🔄 Teacher chat refreshed - " + latestHistory.size() + " messages");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error refreshing teacher chat: " + e.getMessage());
        }
    }
    
    private void updateOnlineStatus(String userId) {
        // Simple implementation - show as available for chat
        Platform.runLater(() -> {
            onlineStatusLabel.setText("Có thể chat");
            onlineStatusLabel.setTextFill(Color.GREEN);
        });
    }
    
    private void showAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
    
    // Custom cell for student list
    private static class StudentListCell extends ListCell<Student> {
        @Override
        protected void updateItem(Student student, boolean empty) {
            super.updateItem(student, empty);
            
            if (empty || student == null) {
                setText(null);
                setGraphic(null);
            } else {
                VBox content = new VBox(2);
                
                String studentName = student.getFullName() != null ? student.getFullName() : "Tên không xác định";
                Label nameLabel = new Label(studentName);
                nameLabel.setFont(Font.font("System", FontWeight.BOLD, 14));

                String className = "Chưa có lớp";
                try {
                    if (student.getClassName() != null && !student.getClassName().trim().isEmpty()) {
                        className = student.getClassName();
                    } else if (student.getMajor() != null && !student.getMajor().trim().isEmpty()) {
                        className = student.getMajor();
                    }
                } catch (Exception e) {
                    className = "Lỗi thông tin";
                }

                Label classLabel = new Label(className);
                classLabel.setFont(Font.font("System", 12));
                classLabel.setTextFill(Color.GRAY);
                
                content.getChildren().addAll(nameLabel, classLabel);
                setGraphic(content);
            }
        }
    }
    
    @FXML
    private void goBackToHome() {
        try {
            // Stop refresh service before leaving
            cleanup();

            // Navigate back to teacher dashboard
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml",
                                   "Teacher Dashboard - " + currentUser.getFullName());

            System.out.println("🏠 Navigating back to Teacher Dashboard");

        } catch (Exception e) {
            System.err.println("❌ Error navigating back to home: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể quay lại trang chủ: " + e.getMessage());
        }
    }

    // New methods for enhanced chat features

    @FXML
    private void attachImage() {
        if (selectedStudent == null) {
            showAlert("Lỗi", "Vui lòng chọn sinh viên để đính kèm ảnh");
            return;
        }

        try {
            Stage stage = (Stage) attachImageButton.getScene().getWindow();
            File imageFile = fileUploadService.chooseImageFile(stage);

            if (imageFile != null) {
                // Validate attachment
                AttachmentPreviewService.AttachmentValidation validation =
                    attachmentPreviewService.validateAttachment(imageFile, true);

                if (!validation.isValid()) {
                    showAlert("Lỗi Đính Kèm", validation.getMessage());
                    return;
                }

                // Remove any existing image attachment
                if (pendingImageAttachment != null) {
                    clearImageAttachment();
                }

                // Set pending attachment and show preview
                pendingImageAttachment = imageFile;
                showImagePreview();

                System.out.println("🖼️ Image attached for preview: " + imageFile.getName());
            }
        } catch (Exception e) {
            System.err.println("❌ Error attaching image: " + e.getMessage());
            showAlert("Lỗi", "Không thể đính kèm ảnh: " + e.getMessage());
        }
    }

    @FXML
    private void attachFile() {
        if (selectedStudent == null) {
            showAlert("Lỗi", "Vui lòng chọn sinh viên để đính kèm file");
            return;
        }

        try {
            Stage stage = (Stage) attachFileButton.getScene().getWindow();
            File file = fileUploadService.chooseDocumentFile(stage);

            if (file != null) {
                // Validate attachment
                AttachmentPreviewService.AttachmentValidation validation =
                    attachmentPreviewService.validateAttachment(file, false);

                if (!validation.isValid()) {
                    showAlert("Lỗi Đính Kèm", validation.getMessage());
                    return;
                }

                // Remove any existing file attachment
                if (pendingFileAttachment != null) {
                    clearFileAttachment();
                }

                // Set pending attachment and show preview
                pendingFileAttachment = file;
                showFilePreview();

                System.out.println("📎 File attached for preview: " + file.getName());
            }
        } catch (Exception e) {
            System.err.println("❌ Error attaching file: " + e.getMessage());
            showAlert("Lỗi", "Không thể đính kèm file: " + e.getMessage());
        }
    }

    @FXML
    private void toggleAudioRecording() {
        if (selectedStudent == null) {
            showAlert("Lỗi", "Vui lòng chọn sinh viên để ghi âm");
            return;
        }

        if (audioRecordingService.isRecording()) {
            stopAudioRecording();
        } else {
            startAudioRecording();
        }
    }

    private void startAudioRecording() {
        try {
            AudioRecordingService.RecordingResult result = audioRecordingService.startRecording(currentUser.getUserId());

            if (result.isSuccess()) {
                recordAudioButton.setText("⏹️");
                recordAudioButton.setTooltip(new Tooltip("Dừng ghi âm"));
                recordingStatusLabel.setText("🎤 Đang ghi âm...");
                recordingStatusLabel.setVisible(true);
                recordingStatusLabel.setTextFill(Color.RED);

                System.out.println("🎤 Audio recording started");
            } else {
                showAlert("Lỗi Ghi Âm", result.getMessage());
            }
        } catch (Exception e) {
            System.err.println("❌ Error starting audio recording: " + e.getMessage());
            showAlert("Lỗi", "Không thể bắt đầu ghi âm: " + e.getMessage());
        }
    }

    private void stopAudioRecording() {
        try {
            AudioRecordingService.RecordingResult result = audioRecordingService.stopRecording();

            recordAudioButton.setText("🎤");
            recordAudioButton.setTooltip(new Tooltip("Ghi âm"));
            recordingStatusLabel.setVisible(false);

            if (result.isSuccess() && result.getAudioInfo() != null) {
                AudioRecordingService.AudioInfo audioInfo = result.getAudioInfo();
                sendAudioMessage(audioInfo, "🎤 Tin nhắn thoại (" + audioInfo.getFormattedDuration() + ")");
                System.out.println("🎤 Audio recording completed: " + audioInfo.getFormattedDuration());
            } else {
                showAlert("Lỗi Ghi Âm", result.getMessage());
            }
        } catch (Exception e) {
            System.err.println("❌ Error stopping audio recording: " + e.getMessage());
            showAlert("Lỗi", "Không thể dừng ghi âm: " + e.getMessage());
        }
    }

    private void sendFileMessage(FileUploadService.FileInfo fileInfo, ChatMessage.MessageType messageType, String displayText) {
        try {
            ChatMessage message = new ChatMessage(
                currentUser.getUserId(),
                currentUser.getFullName(),
                selectedStudent.getUserId(),
                selectedStudent.getFullName(),
                displayText,
                messageType,
                fileInfo.getOriginalName(),
                fileInfo.getFilePath(),
                fileInfo.getMimeType(),
                fileInfo.getFileSize()
            );

            boolean success = chatService.saveMessage(message);

            if (success) {
                currentChatHistory.add(message);
                Platform.runLater(() -> {
                    addMessageToChat(message);
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
            } else {
                showAlert("Lỗi", "Không thể gửi file. Vui lòng thử lại.");
            }
        } catch (Exception e) {
            System.err.println("❌ Error sending file message: " + e.getMessage());
            showAlert("Lỗi", "Không thể gửi file: " + e.getMessage());
        }
    }

    private void sendAudioMessage(AudioRecordingService.AudioInfo audioInfo, String displayText) {
        try {
            ChatMessage message = new ChatMessage(
                currentUser.getUserId(),
                currentUser.getFullName(),
                selectedStudent.getUserId(),
                selectedStudent.getFullName(),
                displayText,
                ChatMessage.MessageType.AUDIO,
                audioInfo.getFileName(),
                audioInfo.getFilePath(),
                audioInfo.getMimeType(),
                audioInfo.getFileSize()
            );

            message.setAudioDuration(audioInfo.getDuration());

            boolean success = chatService.saveMessage(message);

            if (success) {
                currentChatHistory.add(message);
                Platform.runLater(() -> {
                    addMessageToChat(message);
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
            } else {
                showAlert("Lỗi", "Không thể gửi tin nhắn thoại. Vui lòng thử lại.");
            }
        } catch (Exception e) {
            System.err.println("❌ Error sending audio message: " + e.getMessage());
            showAlert("Lỗi", "Không thể gửi tin nhắn thoại: " + e.getMessage());
        }
    }

    private void addImageContent(VBox messageBox, ChatMessage message) {
        try {
            File imageFile = new File(message.getFilePath());
            if (imageFile.exists()) {
                Image image = new Image(imageFile.toURI().toString());
                ImageView imageView = new ImageView(image);
                imageView.setFitWidth(200);
                imageView.setFitHeight(150);
                imageView.setPreserveRatio(true);
                imageView.setSmooth(true);

                // Make image clickable to view full size
                final ChatMessage finalMessage = message;
                imageView.setOnMouseClicked(mouseEvent -> {
                    if (mouseEvent.getClickCount() == 2) { // Double click to open
                        fileDownloadService.openFile(finalMessage.getFilePath());
                    }
                });
                imageView.setStyle("-fx-cursor: hand;");

                // File info with download button
                HBox fileInfoBox = new HBox(10);
                fileInfoBox.setAlignment(Pos.CENTER_LEFT);

                Label fileNameLabel = new Label("🖼️ " + message.getFileName());
                fileNameLabel.setFont(Font.font("System", FontWeight.BOLD, 12));

                Button downloadBtn = new Button("📥");
                downloadBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 10; -fx-background-radius: 10; -fx-padding: 5; -fx-font-size: 12px; -fx-cursor: hand;");
                downloadBtn.setTooltip(new Tooltip("Tải xuống ảnh"));
                downloadBtn.setOnAction(actionEvent -> downloadFile(finalMessage));

                fileInfoBox.getChildren().addAll(fileNameLabel, downloadBtn);
                messageBox.getChildren().addAll(imageView, fileInfoBox);
            } else {
                Label errorLabel = new Label("❌ Không thể tải ảnh");
                errorLabel.setTextFill(Color.RED);
                messageBox.getChildren().add(errorLabel);
            }
        } catch (Exception e) {
            Label errorLabel = new Label("❌ Lỗi hiển thị ảnh");
            errorLabel.setTextFill(Color.RED);
            messageBox.getChildren().add(errorLabel);
        }
    }

    private void addAudioContent(VBox messageBox, ChatMessage message) {
        VBox audioContainer = new VBox(5);

        // Audio info row
        HBox audioInfoBox = new HBox(10);
        audioInfoBox.setAlignment(Pos.CENTER_LEFT);

        Label audioIcon = new Label("🎤");
        audioIcon.setFont(Font.font(20));

        VBox audioInfo = new VBox(2);
        Label fileNameLabel = new Label(message.getFileName());
        fileNameLabel.setFont(Font.font("System", FontWeight.BOLD, 12));

        Label durationLabel = new Label("Thời lượng: " + message.getFormattedAudioDuration());
        durationLabel.setFont(Font.font("System", 10));
        durationLabel.setTextFill(Color.GRAY);

        audioInfo.getChildren().addAll(fileNameLabel, durationLabel);
        audioInfoBox.getChildren().addAll(audioIcon, audioInfo);

        // Control buttons row
        HBox controlsBox = new HBox(10);
        controlsBox.setAlignment(Pos.CENTER_LEFT);

        Button playBtn = new Button("▶️");
        playBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 14px; -fx-cursor: hand;");
        playBtn.setTooltip(new Tooltip("Phát audio"));
        final ChatMessage finalMessage = message;
        final Button finalPlayBtn = playBtn;
        playBtn.setOnAction(actionEvent -> playAudio(finalMessage, finalPlayBtn));

        Button downloadBtn = new Button("📥");
        downloadBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 14px; -fx-cursor: hand;");
        downloadBtn.setTooltip(new Tooltip("Tải xuống audio"));
        downloadBtn.setOnAction(actionEvent -> downloadFile(finalMessage));

        controlsBox.getChildren().addAll(playBtn, downloadBtn);

        audioContainer.getChildren().addAll(audioInfoBox, controlsBox);
        messageBox.getChildren().add(audioContainer);
    }

    private void addFileContent(VBox messageBox, ChatMessage message) {
        VBox fileContainer = new VBox(5);

        // File info row
        HBox fileInfoBox = new HBox(10);
        fileInfoBox.setAlignment(Pos.CENTER_LEFT);

        String fileIcon = fileDownloadService.getFileIcon(message.getFileName());
        Label iconLabel = new Label(fileIcon);
        iconLabel.setFont(Font.font(20));

        VBox fileInfo = new VBox(2);
        Label fileNameLabel = new Label(message.getFileName());
        fileNameLabel.setFont(Font.font("System", FontWeight.BOLD, 12));

        Label fileSizeLabel = new Label("Kích thước: " + message.getFormattedFileSize());
        fileSizeLabel.setFont(Font.font("System", 10));
        fileSizeLabel.setTextFill(Color.GRAY);

        fileInfo.getChildren().addAll(fileNameLabel, fileSizeLabel);
        fileInfoBox.getChildren().addAll(iconLabel, fileInfo);

        // Action buttons row
        HBox actionsBox = new HBox(10);
        actionsBox.setAlignment(Pos.CENTER_LEFT);

        Button openBtn = new Button("📂 Mở");
        openBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand;");
        openBtn.setTooltip(new Tooltip("Mở file với ứng dụng mặc định"));
        final ChatMessage finalMessage = message;
        openBtn.setOnAction(actionEvent -> openFile(finalMessage));

        Button downloadBtn = new Button("📥 Tải");
        downloadBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand;");
        downloadBtn.setTooltip(new Tooltip("Tải xuống file"));
        downloadBtn.setOnAction(actionEvent -> downloadFile(finalMessage));

        actionsBox.getChildren().addAll(openBtn, downloadBtn);

        fileContainer.getChildren().addAll(fileInfoBox, actionsBox);
        messageBox.getChildren().add(fileContainer);
    }

    // File and audio handling methods

    private void downloadFile(ChatMessage message) {
        try {
            if (!fileDownloadService.fileExists(message.getFilePath())) {
                showAlert("Lỗi", "File không tồn tại: " + message.getFileName());
                return;
            }

            Stage stage = (Stage) sendButton.getScene().getWindow();
            FileDownloadService.DownloadResult result = fileDownloadService.downloadFileWithDialog(
                message.getFilePath(), message.getFileName(), stage);

            if (result.isSuccess()) {
                showInfoAlert("Thành công", result.getMessage());
                System.out.println("📥 File downloaded: " + message.getFileName());
            } else {
                showAlert("Lỗi Tải Xuống", result.getMessage());
            }

        } catch (Exception e) {
            System.err.println("❌ Error downloading file: " + e.getMessage());
            showAlert("Lỗi", "Không thể tải xuống file: " + e.getMessage());
        }
    }

    private void openFile(ChatMessage message) {
        try {
            if (!fileDownloadService.fileExists(message.getFilePath())) {
                showAlert("Lỗi", "File không tồn tại: " + message.getFileName());
                return;
            }

            boolean success = fileDownloadService.openFile(message.getFilePath());
            if (success) {
                System.out.println("📂 File opened: " + message.getFileName());
            } else {
                showAlert("Lỗi", "Không thể mở file. Hãy thử tải xuống và mở thủ công.");
            }

        } catch (Exception e) {
            System.err.println("❌ Error opening file: " + e.getMessage());
            showAlert("Lỗi", "Không thể mở file: " + e.getMessage());
        }
    }

    private void playAudio(ChatMessage message, Button playBtn) {
        try {
            if (!fileDownloadService.fileExists(message.getFilePath())) {
                showAlert("Lỗi", "File audio không tồn tại: " + message.getFileName());
                return;
            }

            // Check if this audio is currently playing
            if (audioPlayerService.isPlaying() &&
                message.getFileName().equals(audioPlayerService.getCurrentAudioFile())) {
                // Stop current playback
                audioPlayerService.stopAudio();
                playBtn.setText("▶️");
                playBtn.setTooltip(new Tooltip("Phát audio"));
            } else {
                // Stop any current playback and start new one
                if (audioPlayerService.isPlaying()) {
                    audioPlayerService.stopAudio();
                }

                AudioPlayerService.PlaybackResult result = audioPlayerService.playAudio(
                    message.getFilePath(), message.getFileName());

                if (result.isSuccess()) {
                    playBtn.setText("⏹️");
                    playBtn.setTooltip(new Tooltip("Dừng phát"));
                    System.out.println("🎵 Playing audio: " + message.getFileName());
                } else {
                    showAlert("Lỗi Audio", result.getMessage());
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error playing audio: " + e.getMessage());
            showAlert("Lỗi", "Không thể phát audio: " + e.getMessage());
        }
    }

    private void showInfoAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    // Attachment preview methods

    private void showImagePreview() {
        if (pendingImageAttachment == null) return;

        Platform.runLater(() -> {
            attachmentPreviewArea.getChildren().clear();

            VBox preview = attachmentPreviewService.createImagePreview(pendingImageAttachment, () -> clearImageAttachment());
            attachmentPreviewArea.getChildren().add(preview);
            attachmentPreviewArea.setVisible(true);
        });
    }

    private void showFilePreview() {
        if (pendingFileAttachment == null) return;

        Platform.runLater(() -> {
            attachmentPreviewArea.getChildren().clear();

            VBox preview = attachmentPreviewService.createFilePreview(pendingFileAttachment, () -> clearFileAttachment());
            attachmentPreviewArea.getChildren().add(preview);
            attachmentPreviewArea.setVisible(true);
        });
    }

    private void clearImageAttachment() {
        pendingImageAttachment = null;
        updateAttachmentPreview();
    }

    private void clearFileAttachment() {
        pendingFileAttachment = null;
        updateAttachmentPreview();
    }

    private void clearAllAttachments() {
        pendingImageAttachment = null;
        pendingFileAttachment = null;
        updateAttachmentPreview();
    }

    private void updateAttachmentPreview() {
        Platform.runLater(() -> {
            attachmentPreviewArea.getChildren().clear();

            if (pendingImageAttachment != null) {
                VBox preview = attachmentPreviewService.createImagePreview(pendingImageAttachment, () -> clearImageAttachment());
                attachmentPreviewArea.getChildren().add(preview);
                attachmentPreviewArea.setVisible(true);
            } else if (pendingFileAttachment != null) {
                VBox preview = attachmentPreviewService.createFilePreview(pendingFileAttachment, () -> clearFileAttachment());
                attachmentPreviewArea.getChildren().add(preview);
                attachmentPreviewArea.setVisible(true);
            } else {
                attachmentPreviewArea.setVisible(false);
            }
        });
    }

    // Combo message methods

    private ChatMessage sendTextWithImageMessage(String content) throws Exception {
        FileUploadService.FileUploadResult result = fileUploadService.uploadFile(pendingImageAttachment, currentUser.getUserId());

        if (!result.isSuccess()) {
            throw new Exception("Upload ảnh thất bại: " + result.getMessage());
        }

        ChatMessage message = new ChatMessage(
            currentUser.getUserId(),
            currentUser.getFullName(),
            selectedStudent.getUserId(),
            selectedStudent.getFullName(),
            content,
            ChatMessage.MessageType.TEXT_WITH_IMAGE,
            result.getFileInfo().getOriginalName(),
            result.getFileInfo().getFilePath(),
            result.getFileInfo().getMimeType(),
            result.getFileInfo().getFileSize()
        );

        boolean success = chatService.saveMessage(message);
        if (!success) {
            throw new Exception("Không thể lưu tin nhắn");
        }

        return message;
    }

    private ChatMessage sendTextWithFileMessage(String content) throws Exception {
        FileUploadService.FileUploadResult result = fileUploadService.uploadFile(pendingFileAttachment, currentUser.getUserId());

        if (!result.isSuccess()) {
            throw new Exception("Upload file thất bại: " + result.getMessage());
        }

        ChatMessage message = new ChatMessage(
            currentUser.getUserId(),
            currentUser.getFullName(),
            selectedStudent.getUserId(),
            selectedStudent.getFullName(),
            content,
            ChatMessage.MessageType.TEXT_WITH_FILE,
            result.getFileInfo().getOriginalName(),
            result.getFileInfo().getFilePath(),
            result.getFileInfo().getMimeType(),
            result.getFileInfo().getFileSize()
        );

        boolean success = chatService.saveMessage(message);
        if (!success) {
            throw new Exception("Không thể lưu tin nhắn");
        }

        return message;
    }

    private ChatMessage sendImageOnlyMessage() throws Exception {
        FileUploadService.FileUploadResult result = fileUploadService.uploadFile(pendingImageAttachment, currentUser.getUserId());

        if (!result.isSuccess()) {
            throw new Exception("Upload ảnh thất bại: " + result.getMessage());
        }

        ChatMessage message = new ChatMessage(
            currentUser.getUserId(),
            currentUser.getFullName(),
            selectedStudent.getUserId(),
            selectedStudent.getFullName(),
            "🖼️ " + result.getFileInfo().getOriginalName(),
            ChatMessage.MessageType.IMAGE,
            result.getFileInfo().getOriginalName(),
            result.getFileInfo().getFilePath(),
            result.getFileInfo().getMimeType(),
            result.getFileInfo().getFileSize()
        );

        boolean success = chatService.saveMessage(message);
        if (!success) {
            throw new Exception("Không thể lưu tin nhắn");
        }

        return message;
    }

    private ChatMessage sendFileOnlyMessage() throws Exception {
        FileUploadService.FileUploadResult result = fileUploadService.uploadFile(pendingFileAttachment, currentUser.getUserId());

        if (!result.isSuccess()) {
            throw new Exception("Upload file thất bại: " + result.getMessage());
        }

        ChatMessage message = new ChatMessage(
            currentUser.getUserId(),
            currentUser.getFullName(),
            selectedStudent.getUserId(),
            selectedStudent.getFullName(),
            "📎 " + result.getFileInfo().getOriginalName(),
            ChatMessage.MessageType.FILE,
            result.getFileInfo().getOriginalName(),
            result.getFileInfo().getFilePath(),
            result.getFileInfo().getMimeType(),
            result.getFileInfo().getFileSize()
        );

        boolean success = chatService.saveMessage(message);
        if (!success) {
            throw new Exception("Không thể lưu tin nhắn");
        }

        return message;
    }

    private ChatMessage sendTextOnlyMessage(String content) throws Exception {
        ChatMessage message = new ChatMessage(
            currentUser.getUserId(),
            currentUser.getFullName(),
            selectedStudent.getUserId(),
            selectedStudent.getFullName(),
            content
        );

        boolean success = chatService.saveMessage(message);
        if (!success) {
            throw new Exception("Không thể lưu tin nhắn");
        }

        return message;
    }

    public void cleanup() {
        // Stop audio playback if in progress
        if (audioPlayerService != null) {
            audioPlayerService.cleanup();
        }

        // Stop audio recording if in progress
        if (audioRecordingService != null && audioRecordingService.isRecording()) {
            audioRecordingService.cancelRecording();
        }

        if (refreshService != null && !refreshService.isShutdown()) {
            refreshService.shutdown();
            System.out.println("🔄 Teacher chat refresh service stopped");
        }
    }
}

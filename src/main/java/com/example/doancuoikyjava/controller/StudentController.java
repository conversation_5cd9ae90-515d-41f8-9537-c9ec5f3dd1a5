package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.CourseService;
import com.example.doancuoikyjava.service.ExcelExportHelper;
import com.example.doancuoikyjava.service.GradeService;
import com.example.doancuoikyjava.service.ScheduleService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.image.ImageView;
import com.example.doancuoikyjava.util.AvatarUtils;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.ResourceBundle;

public class StudentController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button logoutButton;
    @FXML private Button myCoursesBtn;
    @FXML private Button gradesBtn;
    @FXML private Button scheduleBtn;
    @FXML private Button enrollBtn;
    @FXML private Button exportBtn;
    @FXML private Button profileBtn;
    
    @FXML private Label enrolledCoursesLabel;
    @FXML private Label totalCreditsLabel;
    @FXML private Label gpaLabel;
    
    @FXML private TableView<Course> coursesTableView;
    @FXML private TableColumn<Course, String> courseIdColumn;
    @FXML private TableColumn<Course, String> courseNameColumn;
    @FXML private TableColumn<Course, Integer> creditsColumn;
    @FXML private TableColumn<Course, String> teacherColumn;
    @FXML private TableColumn<Course, String> scheduleColumn;
    
    @FXML private TableView<Grade> recentGradesTableView;
    @FXML private TableColumn<Grade, String> gradeSubjectColumn;
    @FXML private TableColumn<Grade, Double> gradeScoreColumn;
    @FXML private TableColumn<Grade, String> gradeLetterColumn;
    @FXML private TableColumn<Grade, LocalDate> gradeDateColumn;
    
    @FXML private ListView<String> todayScheduleListView;
    @FXML private ListView<String> notificationsListView;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private Student currentStudent;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        
        setupCurrentStudent();
        setupWelcomeMessage();
        setupTableColumns();
        loadDashboardData();
        setupScheduleAndNotifications();
    }
    
    private void setupCurrentStudent() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Student) {
            currentStudent = (Student) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentStudent != null) {
            welcomeLabel.setText("Xin chào, " + currentStudent.getFullName());
        }
    }
    
    private void setupTableColumns() {
        // Courses table
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        teacherColumn.setCellValueFactory(new PropertyValueFactory<>("teacherName"));
        scheduleColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(course));
        });
        
        // Grades table
        gradeSubjectColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        gradeScoreColumn.setCellValueFactory(new PropertyValueFactory<>("score"));
        gradeLetterColumn.setCellValueFactory(new PropertyValueFactory<>("letterGrade"));
        gradeDateColumn.setCellValueFactory(new PropertyValueFactory<>("dateRecorded"));
    }
    
    private void loadDashboardData() {
        if (currentStudent == null) return;
        
        // Load enrolled courses
        List<Course> allCourses = courseService.getAllCourses();
        List<Course> enrolledCourses = allCourses.stream()
                .filter(course -> course.getEnrolledStudents().contains(currentStudent.getStudentId()))
                .toList();
        
        // Update statistics
        enrolledCoursesLabel.setText(String.valueOf(enrolledCourses.size()));
        
        int totalCredits = enrolledCourses.stream()
                .mapToInt(Course::getCredits)
                .sum();
        totalCreditsLabel.setText(String.valueOf(totalCredits));
        
        // Display GPA
        gpaLabel.setText(String.format("%.2f", currentStudent.getGpa()));
        
        // Load courses into table
        ObservableList<Course> coursesList = FXCollections.observableArrayList(enrolledCourses);
        coursesTableView.setItems(coursesList);
        
        // Load recent grades
        loadRecentGrades();
    }
    
    private void loadRecentGrades() {
        // Create sample grades for demo
        ObservableList<Grade> recentGrades = FXCollections.observableArrayList();
        
        if (currentStudent != null && !currentStudent.getGrades().isEmpty()) {
            recentGrades.addAll(currentStudent.getGrades());
        } else {
            // Sample data for demo
            Grade grade1 = new Grade("G001", currentStudent != null ? currentStudent.getStudentId() : "STU001", 
                                   "CS101", "Lập trình Java", 85.0, 3, "2024-1", "TCH001");
            Grade grade2 = new Grade("G002", currentStudent != null ? currentStudent.getStudentId() : "STU001", 
                                   "CS102", "Cơ sở dữ liệu", 78.0, 3, "2024-1", "TCH001");
            recentGrades.addAll(List.of(grade1, grade2));
        }
        
        recentGradesTableView.setItems(recentGrades);
    }
    
    private void setupScheduleAndNotifications() {
        // Setup today's schedule
        ObservableList<String> todaySchedule = FXCollections.observableArrayList(
            "07:30 - 09:30: Lập trình Java - Phòng A101",
            "09:45 - 11:45: Cơ sở dữ liệu - Phòng A102",
            "13:30 - 15:30: Thực hành Java - Lab B201"
        );
        todayScheduleListView.setItems(todaySchedule);
        
        // Setup notifications
        ObservableList<String> notifications = FXCollections.observableArrayList(
            "📝 Điểm môn Lập trình Java đã được cập nhật",
            "📚 Thời gian đăng ký môn học kỳ mới: 15/12 - 25/12",
            "📅 Lịch thi cuối kỳ đã được công bố",
            "💡 Nhắc nhở: Nộp bài tập lớn trước 20/12"
        );
        notificationsListView.setItems(notifications);
    }
    
    @FXML
    private void handleLogout() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận đăng xuất");
        alert.setHeaderText("Bạn có chắc chắn muốn đăng xuất?");
        alert.setContentText("Tất cả dữ liệu chưa lưu sẽ bị mất.");
        
        if (alert.showAndWait().get() == ButtonType.OK) {
            SceneManager.logout();
        }
    }
    

    
    @FXML
    private void showGrades() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-grades.fxml",
                                   "Xem điểm - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang xem điểm: " + e.getMessage());
        }
    }
    
    @FXML
    private void showSchedule() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-schedule.fxml",
                                   "Lịch học - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang lịch học: " + e.getMessage());
        }
    }
    
    @FXML
    private void showEnrollment() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-enrollment.fxml",
                                   "Đăng ký môn học - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang đăng ký môn học: " + e.getMessage());
        }
    }
    
    @FXML
    private void showProfile() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/simple-profile.fxml",
                                   "Thông tin cá nhân - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang thông tin cá nhân: " + e.getMessage());
        }
    }

    @FXML
    private void showChat() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-chat.fxml", "Chat với Giáo viên");
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang chat: " + e.getMessage());
        }
    }

    @FXML
    private void showNotifications() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/notification-view.fxml",
                                   "Thông báo - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang thông báo: " + e.getMessage());
        }
    }

    @FXML
    private void showMyCourses() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-my-courses.fxml",
                                   "Môn học của tôi - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang môn học: " + e.getMessage());
        }
    }

    @FXML
    private void showExportOptions() {
        showStudentExportDialog();
    }
    
    /**
     * Extract time information from course schedule
     */
    private String extractTimeFromSchedule(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's time range
                return schedules.get(0).getTimeRange();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = course.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("-")) {
                    String[] parts = schedule.split("-");
                    if (parts.length >= 2) {
                        // Look for time pattern like "7:30-9:30"
                        String timePart = parts[parts.length - 1].trim();
                        if (timePart.matches(".*\\d{1,2}:\\d{2}.*")) {
                            return timePart;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting time from schedule: " + e.getMessage());
        }

        return "Chưa xếp";
    }

    private void showStudentExportDialog() {
        try {
            System.out.println("📊 Opening student Excel export dialog...");

            Alert exportDialog = new Alert(Alert.AlertType.CONFIRMATION);
            exportDialog.setTitle("📊 Xuất dữ liệu Excel");
            exportDialog.setHeaderText("Chọn loại dữ liệu muốn xuất:");

            // Create custom buttons for student
            ButtonType myGradesBtn = new ButtonType("📝 Bảng điểm của tôi");
            ButtonType myCoursesBtn = new ButtonType("📚 Môn học đã đăng ký");
            ButtonType cancelBtn = new ButtonType("❌ Hủy", ButtonBar.ButtonData.CANCEL_CLOSE);

            exportDialog.getButtonTypes().setAll(myGradesBtn, myCoursesBtn, cancelBtn);

            exportDialog.showAndWait().ifPresent(response -> {
                ExcelExportHelper exportHelper = new ExcelExportHelper();
                String filePath = null;

                User currentUser = SceneManager.getCurrentUser();
                if (!(currentUser instanceof Student)) {
                    showAlert("Lỗi", "❌ Chỉ sinh viên mới có thể sử dụng tính năng này!");
                    return;
                }

                Student currentStudent = (Student) currentUser;
                String studentId = currentStudent.getStudentId();

                if (response == myGradesBtn) {
                    // Export grades for this student
                    GradeService gradeService = new GradeService();
                    filePath = exportHelper.exportStudentGrades(studentId, gradeService.getAllGrades());

                } else if (response == myCoursesBtn) {
                    // Export courses enrolled by this student
                    showAlert("Thông báo", "🚧 Tính năng xuất danh sách môn học đang được phát triển!");
                    return;
                }

                if (filePath != null) {
                    showAlert("Thành công", "✅ Đã xuất dữ liệu thành công!\n\n" +
                            "📁 File đã được lưu tại:\n" + filePath + "\n\n" +
                            "💡 Bạn có thể mở file bằng Microsoft Excel hoặc LibreOffice Calc.");
                } else {
                    showAlert("Lỗi", "❌ Không thể xuất dữ liệu!\n\nVui lòng thử lại sau.");
                }
            });

        } catch (Exception e) {
            System.err.println("❌ Error showing student export dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "❌ Không thể mở dialog xuất Excel: " + e.getMessage());
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}

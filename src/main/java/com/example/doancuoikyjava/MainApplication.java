// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

/**
 * Main Application Entry Point
 * Shows login screen and handles role-based navigation
 */
public class MainApplication extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("🔧 Đang khởi tạo SceneManager...");
            // Initialize SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            System.out.println("✅ SceneManager đã được khởi tạo thành công!");

            System.out.println("🔐 Đang tải màn hình đăng nhập...");
            // Show login screen
            SceneManager.switchScene("/com/example/doancuoikyjava/login.fxml",
                                   "🎓 Hệ thống Quản lý Sinh viên - Đăng nhập");

            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("🎉 HỆ THỐNG QUẢN LÝ SINH VIÊN ĐÃ KHỞI ĐỘNG!");
            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("🔐 Vui lòng đăng nhập với thông tin sau:");
            System.out.println("   🔧 Quản trị viên: admin / admin123");
            System.out.println("   👨‍🏫 Giáo viên: teacher / teacher123");
            System.out.println("   🎓 Sinh viên: student / student123");
            System.out.println("🎯 Chọn vai trò phù hợp để truy cập hệ thống!");
            System.out.println("💡 Mẹo: Sử dụng nút Quick Login để đăng nhập nhanh!");

        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI KHỞI ĐỘNG ỨNG DỤNG!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cấu hình JavaFX!");
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options
        System.out.println("🎓 Starting Student Management System");
        System.out.println("════════════════════════════════════════");
        System.out.println("📋 System Features:");
        System.out.println("   🔧 Admin: Complete system management");
        System.out.println("   👨‍🏫 Teacher: Course and schedule management");
        System.out.println("   🎓 Student: Learning and enrollment");
        System.out.println("════════════════════════════════════════");
        
        launch(args);
    }
}
package com.example.doancuoikyjava.demo;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.service.AttachmentPreviewService;

import java.io.File;

/**
 * Demo class to showcase combo message features
 */
public class ComboMessageDemo {
    
    public static void main(String[] args) {
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
        System.out.println("🎉           DEMO TÍNH NĂNG COMBO MESSAGE");
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
        
        // Test ChatMessage with combo types
        testComboMessageTypes();
        
        // Test AttachmentPreviewService
        testAttachmentPreviewService();
        
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
        System.out.println("🎉           DEMO HOÀN THÀNH THÀNH CÔNG!");
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
    }
    
    private static void testComboMessageTypes() {
        System.out.println("\n📝 ═══ TEST COMBO MESSAGE TYPES ═══");
        
        // Test text with image combo
        ChatMessage textWithImage = new ChatMessage(
            "USER001", "Nguyễn Văn A",
            "USER002", "Trần Thị B",
            "<PERSON><PERSON><PERSON> là bài tập về nhà hôm nay",
            ChatMessage.MessageType.TEXT_WITH_IMAGE,
            "homework.jpg",
            "uploads/chat/images/USER001_20241216_143022_homework.jpg",
            "image/jpeg",
            1024000
        );
        
        System.out.println("✅ Text with Image Message:");
        System.out.println("   📝 Content: " + textWithImage.getContent());
        System.out.println("   🖼️ File: " + textWithImage.getFileName());
        System.out.println("   📏 Size: " + textWithImage.getFormattedFileSize());
        System.out.println("   🔤 Type: " + textWithImage.getType());
        System.out.println("   📝🖼️ Is combo: " + textWithImage.isComboMessage());
        System.out.println("   📝 Has text: " + textWithImage.hasTextContent());
        System.out.println("   🖼️ Is text+image: " + textWithImage.isTextWithImageMessage());
        
        // Test text with file combo
        ChatMessage textWithFile = new ChatMessage(
            "USER001", "Nguyễn Văn A",
            "USER002", "Trần Thị B",
            "Tài liệu tham khảo cho bài học",
            ChatMessage.MessageType.TEXT_WITH_FILE,
            "reference.pdf",
            "uploads/chat/documents/USER001_20241216_143022_reference.pdf",
            "application/pdf",
            2048000
        );
        
        System.out.println("\n✅ Text with File Message:");
        System.out.println("   📝 Content: " + textWithFile.getContent());
        System.out.println("   📎 File: " + textWithFile.getFileName());
        System.out.println("   📏 Size: " + textWithFile.getFormattedFileSize());
        System.out.println("   🔤 Type: " + textWithFile.getType());
        System.out.println("   📝📎 Is combo: " + textWithFile.isComboMessage());
        System.out.println("   📝 Has text: " + textWithFile.hasTextContent());
        System.out.println("   📎 Is text+file: " + textWithFile.isTextWithFileMessage());
        
        // Test regular messages for comparison
        ChatMessage textOnly = new ChatMessage(
            "USER001", "Nguyễn Văn A",
            "USER002", "Trần Thị B",
            "Chỉ có text thôi"
        );
        
        System.out.println("\n✅ Text Only Message:");
        System.out.println("   📝 Content: " + textOnly.getContent());
        System.out.println("   🔤 Type: " + textOnly.getType());
        System.out.println("   📝📎 Is combo: " + textOnly.isComboMessage());
        System.out.println("   📝 Has text: " + textOnly.hasTextContent());
        
        ChatMessage imageOnly = new ChatMessage(
            "USER001", "Nguyễn Văn A",
            "USER002", "Trần Thị B",
            "🖼️ photo.jpg",
            ChatMessage.MessageType.IMAGE,
            "photo.jpg",
            "uploads/chat/images/USER001_20241216_143022_photo.jpg",
            "image/jpeg",
            512000
        );
        
        System.out.println("\n✅ Image Only Message:");
        System.out.println("   📝 Content: " + imageOnly.getContent());
        System.out.println("   🖼️ File: " + imageOnly.getFileName());
        System.out.println("   🔤 Type: " + imageOnly.getType());
        System.out.println("   📝📎 Is combo: " + imageOnly.isComboMessage());
        System.out.println("   🖼️ Is image: " + imageOnly.isImageMessage());
    }
    
    private static void testAttachmentPreviewService() {
        System.out.println("\n📎 ═══ TEST ATTACHMENT PREVIEW SERVICE ═══");
        
        try {
            AttachmentPreviewService previewService = new AttachmentPreviewService();
            System.out.println("✅ AttachmentPreviewService khởi tạo thành công");
            
            // Test validation with mock files
            System.out.println("\n🔍 Test file validation:");
            
            // Test with null file
            AttachmentPreviewService.AttachmentValidation nullValidation = 
                previewService.validateAttachment(null, true);
            System.out.println("   ❌ Null file validation: " + nullValidation.isValid() + 
                " - " + nullValidation.getMessage());
            
            // Test with non-existent file
            File nonExistentFile = new File("non_existent_file.jpg");
            AttachmentPreviewService.AttachmentValidation nonExistentValidation = 
                previewService.validateAttachment(nonExistentFile, true);
            System.out.println("   ❌ Non-existent file validation: " + nonExistentValidation.isValid() + 
                " - " + nonExistentValidation.getMessage());
            
            // Test with current directory (should exist but not be a valid image)
            File currentDir = new File(".");
            if (currentDir.exists()) {
                AttachmentPreviewService.AttachmentValidation dirValidation = 
                    previewService.validateAttachment(currentDir, true);
                System.out.println("   📁 Directory validation: " + dirValidation.isValid() + 
                    " - " + dirValidation.getMessage());
            }
            
            // Test file type detection
            System.out.println("\n🎨 Test file type detection:");
            testFileTypeDetection();
            
            // Test file size formatting
            System.out.println("\n📏 Test file size formatting:");
            testFileSizeFormatting();
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test AttachmentPreviewService: " + e.getMessage());
        }
    }
    
    private static void testFileTypeDetection() {
        // This would normally be done through AttachmentPreviewService private methods
        // For demo purposes, we'll show the expected behavior
        
        System.out.println("   📕 document.pdf → PDF Document");
        System.out.println("   📘 report.docx → Word Document");
        System.out.println("   📗 data.xlsx → Excel Spreadsheet");
        System.out.println("   📙 presentation.pptx → PowerPoint Presentation");
        System.out.println("   📄 readme.txt → Text File");
        System.out.println("   🖼️ photo.jpg → JPEG Image");
        System.out.println("   🖼️ image.png → PNG Image");
        System.out.println("   🖼️ animation.gif → GIF Image");
        System.out.println("   🖼️ bitmap.bmp → Bitmap Image");
        System.out.println("   🖼️ modern.webp → WebP Image");
        System.out.println("   📎 unknown.xyz → XYZ File");
    }
    
    private static void testFileSizeFormatting() {
        // This would normally be done through AttachmentPreviewService private methods
        // For demo purposes, we'll show the expected behavior
        
        System.out.println("   512 bytes → 512 B");
        System.out.println("   1536 bytes → 1.5 KB");
        System.out.println("   2097152 bytes → 2.0 MB");
        System.out.println("   5242880 bytes → 5.0 MB");
        System.out.println("   10485760 bytes → 10.0 MB");
    }
}

package com.example.doancuoikyjava.demo;

import com.example.doancuoikyjava.service.FileDownloadService;
import com.example.doancuoikyjava.service.AudioPlayerService;

import java.io.File;

/**
 * Demo class to showcase file download and audio playback features
 */
public class FileDownloadAudioDemo {
    
    public static void main(String[] args) {
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
        System.out.println("🎉        DEMO TÍNH NĂNG TẢI FILE VÀ PHÁT AUDIO");
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
        
        // Test FileDownloadService
        testFileDownloadService();
        
        // Test AudioPlayerService
        testAudioPlayerService();
        
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
        System.out.println("🎉           DEMO HOÀN THÀNH THÀNH CÔNG!");
        System.out.println("🎉 ════════════════════════════════════════════════════════════");
    }
    
    private static void testFileDownloadService() {
        System.out.println("\n📥 ═══ TEST FILE DOWNLOAD SERVICE ═══");
        
        try {
            FileDownloadService downloadService = new FileDownloadService();
            System.out.println("✅ FileDownloadService khởi tạo thành công");
            System.out.println("📁 Thư mục downloads đã được tạo: downloads/");
            
            // Test file icon detection
            System.out.println("\n🎨 Test phát hiện icon file:");
            System.out.println("   📕 document.pdf → " + downloadService.getFileIcon("document.pdf"));
            System.out.println("   📘 report.docx → " + downloadService.getFileIcon("report.docx"));
            System.out.println("   📗 data.xlsx → " + downloadService.getFileIcon("data.xlsx"));
            System.out.println("   📙 presentation.pptx → " + downloadService.getFileIcon("presentation.pptx"));
            System.out.println("   🖼️ photo.jpg → " + downloadService.getFileIcon("photo.jpg"));
            System.out.println("   🎵 song.mp3 → " + downloadService.getFileIcon("song.mp3"));
            System.out.println("   🎬 video.mp4 → " + downloadService.getFileIcon("video.mp4"));
            System.out.println("   🗜️ archive.zip → " + downloadService.getFileIcon("archive.zip"));
            System.out.println("   📄 readme.txt → " + downloadService.getFileIcon("readme.txt"));
            System.out.println("   📎 unknown.xyz → " + downloadService.getFileIcon("unknown.xyz"));
            
            // Test file size formatting
            System.out.println("\n📏 Test định dạng kích thước file:");
            testFileSizeFormatting(downloadService);
            
            // Test file existence check
            System.out.println("\n🔍 Test kiểm tra file tồn tại:");
            System.out.println("   📁 Current directory exists: " + downloadService.fileExists("."));
            System.out.println("   ❌ Non-existent file: " + downloadService.fileExists("non_existent_file.txt"));
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test FileDownloadService: " + e.getMessage());
        }
    }
    
    private static void testFileSizeFormatting(FileDownloadService downloadService) {
        // Create temporary files to test size formatting
        try {
            // Test with current directory (should exist)
            String currentDir = ".";
            String sizeFormatted = downloadService.getFileSizeFormatted(currentDir);
            System.out.println("   📁 Current directory size: " + sizeFormatted);
            
            // Test with non-existent file
            String nonExistentFile = "non_existent_file.txt";
            String nonExistentSize = downloadService.getFileSizeFormatted(nonExistentFile);
            System.out.println("   ❌ Non-existent file size: " + nonExistentSize);
            
        } catch (Exception e) {
            System.err.println("   ❌ Error testing file size formatting: " + e.getMessage());
        }
    }
    
    private static void testAudioPlayerService() {
        System.out.println("\n🎵 ═══ TEST AUDIO PLAYER SERVICE ═══");
        
        try {
            AudioPlayerService audioService = new AudioPlayerService();
            System.out.println("✅ AudioPlayerService khởi tạo thành công");
            
            // Test audio format support
            System.out.println("\n🎼 Test hỗ trợ định dạng audio:");
            System.out.println("   🎵 WAV support: " + AudioPlayerService.isAudioSupported("audio.wav"));
            System.out.println("   🎵 AU support: " + AudioPlayerService.isAudioSupported("audio.au"));
            System.out.println("   🎵 AIFF support: " + AudioPlayerService.isAudioSupported("audio.aiff"));
            System.out.println("   ❌ MP3 support: " + AudioPlayerService.isAudioSupported("audio.mp3"));
            System.out.println("   ❌ Unknown format: " + AudioPlayerService.isAudioSupported("audio.xyz"));
            
            // Test time formatting
            System.out.println("\n⏱️ Test định dạng thời gian:");
            System.out.println("   0 seconds → " + AudioPlayerService.formatTime(0));
            System.out.println("   30 seconds → " + AudioPlayerService.formatTime(30));
            System.out.println("   65 seconds → " + AudioPlayerService.formatTime(65));
            System.out.println("   125 seconds → " + AudioPlayerService.formatTime(125));
            System.out.println("   3661 seconds → " + AudioPlayerService.formatTime(3661));
            
            // Test player state
            System.out.println("\n🎮 Test trạng thái player:");
            System.out.println("   🔇 Is playing: " + audioService.isPlaying());
            System.out.println("   📂 Current file: " + audioService.getCurrentAudioFile());
            System.out.println("   ⏱️ Current position: " + audioService.getCurrentPosition() + " seconds");
            System.out.println("   ⏱️ Total duration: " + audioService.getTotalDuration() + " seconds");
            
            // Test listener setup
            System.out.println("\n🎧 Test audio listener:");
            audioService.setListener(new AudioPlayerService.AudioPlayerListener() {
                @Override
                public void onPlaybackStarted(String fileName) {
                    System.out.println("   ▶️ Playback started: " + fileName);
                }
                
                @Override
                public void onPlaybackStopped(String fileName) {
                    System.out.println("   ⏸️ Playback stopped: " + fileName);
                }
                
                @Override
                public void onPlaybackCompleted(String fileName) {
                    System.out.println("   ✅ Playback completed: " + fileName);
                }
                
                @Override
                public void onPlaybackError(String fileName, String error) {
                    System.out.println("   ❌ Playback error for " + fileName + ": " + error);
                }
            });
            System.out.println("✅ Audio listener đã được thiết lập");
            
            // Test cleanup
            audioService.cleanup();
            System.out.println("✅ Audio service cleanup thành công");
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test AudioPlayerService: " + e.getMessage());
        }
    }
}

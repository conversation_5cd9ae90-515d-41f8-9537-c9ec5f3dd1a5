package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.ChatMessage;

/**
 * Test class to verify addMessageToChat fixes
 */
public class AddMessageToChatTest {
    
    public static void main(String[] args) {
        System.out.println("🔧 ════════════════════════════════════════════════════════════");
        System.out.println("🔧         TEST addMessageToChat() FIXES");
        System.out.println("🔧 ════════════════════════════════════════════════════════════");
        
        // Test combo message types
        testComboMessageTypes();
        
        // Test lambda fixes
        testLambdaFixes();
        
        System.out.println("🔧 ════════════════════════════════════════════════════════════");
        System.out.println("🔧         addMessageToChat() FIXES HOÀN THÀNH!");
        System.out.println("🔧 ════════════════════════════════════════════════════════════");
    }
    
    private static void testComboMessageTypes() {
        System.out.println("\n✅ ═══ TEST COMBO MESSAGE TYPES ═══");
        
        // Test text with image message
        ChatMessage textWithImage = new ChatMessage(
            "USER001", "Teacher A",
            "USER002", "Student B", 
            "<PERSON><PERSON><PERSON> là bài tập về nhà",
            ChatMessage.MessageType.TEXT_WITH_IMAGE,
            "homework.jpg",
            "uploads/chat/images/homework.jpg",
            "image/jpeg",
            1024000
        );
        
        System.out.println("✅ Text with Image Message:");
        System.out.println("   📝 Content: " + textWithImage.getContent());
        System.out.println("   🖼️ File: " + textWithImage.getFileName());
        System.out.println("   🔤 Type: " + textWithImage.getType());
        System.out.println("   📝🖼️ Is text+image: " + textWithImage.isTextWithImageMessage());
        System.out.println("   📝 Has text content: " + textWithImage.hasTextContent());
        System.out.println("   📎 Has file: " + textWithImage.hasFile());
        System.out.println();
        
        // Test text with file message
        ChatMessage textWithFile = new ChatMessage(
            "USER001", "Teacher A",
            "USER002", "Student B",
            "Tài liệu tham khảo",
            ChatMessage.MessageType.TEXT_WITH_FILE,
            "reference.pdf",
            "uploads/chat/documents/reference.pdf",
            "application/pdf",
            2048000
        );
        
        System.out.println("✅ Text with File Message:");
        System.out.println("   📝 Content: " + textWithFile.getContent());
        System.out.println("   📎 File: " + textWithFile.getFileName());
        System.out.println("   🔤 Type: " + textWithFile.getType());
        System.out.println("   📝📎 Is text+file: " + textWithFile.isTextWithFileMessage());
        System.out.println("   📝 Has text content: " + textWithFile.hasTextContent());
        System.out.println("   📎 Has file: " + textWithFile.hasFile());
        System.out.println();
        
        // Test regular messages
        ChatMessage textOnly = new ChatMessage(
            "USER001", "Teacher A",
            "USER002", "Student B",
            "Chỉ có text"
        );
        
        System.out.println("✅ Text Only Message:");
        System.out.println("   📝 Content: " + textOnly.getContent());
        System.out.println("   🔤 Type: " + textOnly.getType());
        System.out.println("   📝📎 Is combo: " + textOnly.isComboMessage());
        System.out.println("   📝 Has text content: " + textOnly.hasTextContent());
        System.out.println();
        
        ChatMessage imageOnly = new ChatMessage(
            "USER001", "Teacher A",
            "USER002", "Student B",
            "🖼️ photo.jpg",
            ChatMessage.MessageType.IMAGE,
            "photo.jpg",
            "uploads/chat/images/photo.jpg",
            "image/jpeg",
            512000
        );
        
        System.out.println("✅ Image Only Message:");
        System.out.println("   📝 Content: " + imageOnly.getContent());
        System.out.println("   🖼️ File: " + imageOnly.getFileName());
        System.out.println("   🔤 Type: " + imageOnly.getType());
        System.out.println("   🖼️ Is image: " + imageOnly.isImageMessage());
        System.out.println("   📝📎 Is combo: " + imageOnly.isComboMessage());
    }
    
    private static void testLambdaFixes() {
        System.out.println("\n🔧 ═══ TEST LAMBDA FIXES ═══");
        
        System.out.println("✅ TeacherChatController.addMessageToChat():");
        System.out.println("   ✅ Added combo message handling:");
        System.out.println("      if (message.isTextWithImageMessage() && message.hasFile()) {");
        System.out.println("          // Show text first, then image");
        System.out.println("          if (message.hasTextContent()) {");
        System.out.println("              contentLabel = new Label(message.getContent());");
        System.out.println("              messageBox.getChildren().add(contentLabel);");
        System.out.println("          }");
        System.out.println("          addImageContent(messageBox, message);");
        System.out.println("      }");
        System.out.println();
        
        System.out.println("✅ StudentChatController.addMessageToChat():");
        System.out.println("   ✅ Added combo message handling (same as Teacher)");
        System.out.println("   ✅ Fixed lambda variable conflicts");
        System.out.println();
        
        System.out.println("✅ Lambda Variable Fixes:");
        System.out.println("   ❌ Before: final ChatMessage finalMessage = message; // Duplicate!");
        System.out.println("   ✅ After:  final ChatMessage finalMessage = message; // Single declaration");
        System.out.println();
        System.out.println("   ❌ Before: imageView.setOnMouseClicked(e -> { ... });");
        System.out.println("   ✅ After:  imageView.setOnMouseClicked(mouseEvent -> { ... });");
        System.out.println();
        
        System.out.println("🎯 Message Display Logic:");
        System.out.println("   1️⃣ TEXT_WITH_IMAGE: Show text → Show image with download");
        System.out.println("   2️⃣ TEXT_WITH_FILE: Show text → Show file with open/download");
        System.out.println("   3️⃣ IMAGE: Show image only with download");
        System.out.println("   4️⃣ AUDIO: Show audio player with play/download");
        System.out.println("   5️⃣ FILE: Show file info with open/download");
        System.out.println("   6️⃣ TEXT: Show text only");
        System.out.println();
        
        System.out.println("🎨 UI Layout for Combo Messages:");
        System.out.println("   ┌─────────────────────────────────────┐");
        System.out.println("   │ Đây là bài tập về nhà hôm nay       │");
        System.out.println("   │                                     │");
        System.out.println("   │ [Image thumbnail 200x150px]        │");
        System.out.println("   │ 🖼️ homework.jpg              [📥] │");
        System.out.println("   │ 14:30                               │");
        System.out.println("   └─────────────────────────────────────┘");
        System.out.println();
        
        System.out.println("   ┌─────────────────────────────────────┐");
        System.out.println("   │ Tài liệu tham khảo cho bài học      │");
        System.out.println("   │                                     │");
        System.out.println("   │ 📕 reference.pdf                   │");
        System.out.println("   │    Kích thước: 2.0 MB              │");
        System.out.println("   │ [📂 Mở] [📥 Tải]                   │");
        System.out.println("   │ 14:32                               │");
        System.out.println("   └─────────────────────────────────────┘");
    }
}

package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.service.FileUploadService;
import com.example.doancuoikyjava.service.AudioRecordingService;

/**
 * Simple test to check if our enhanced chat classes compile correctly
 */
public class CompileTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 ════════════════════════════════════════════════════════════");
        System.out.println("🧪           KIỂM TRA COMPILE CÁC CLASS MỚI");
        System.out.println("🧪 ════════════════════════════════════════════════════════════");
        
        try {
            // Test ChatMessage with new features
            testChatMessage();
            
            // Test FileUploadService
            testFileUploadService();
            
            // Test AudioRecordingService
            testAudioRecordingService();
            
            System.out.println("✅ ════════════════════════════════════════════════════════════");
            System.out.println("✅           TẤT CẢ CLASS COMPILE THÀNH CÔNG!");
            System.out.println("✅ ════════════════════════════════════════════════════════════");
            
        } catch (Exception e) {
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            System.err.println("❌           LỖI COMPILE: " + e.getMessage());
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            e.printStackTrace();
        }
    }
    
    private static void testChatMessage() {
        System.out.println("\n📝 Testing ChatMessage class...");
        
        // Test basic text message
        ChatMessage textMsg = new ChatMessage(
            "USER001", "Test User", 
            "USER002", "Test Receiver", 
            "Hello World!"
        );
        System.out.println("✅ Text message created: " + textMsg.getContent());
        
        // Test image message
        ChatMessage imageMsg = new ChatMessage(
            "USER001", "Test User",
            "USER002", "Test Receiver",
            "🖼️ Test Image",
            ChatMessage.MessageType.IMAGE,
            "test.jpg",
            "/path/to/test.jpg",
            "image/jpeg",
            1024000
        );
        System.out.println("✅ Image message created: " + imageMsg.getFileName());
        System.out.println("   📏 Size: " + imageMsg.getFormattedFileSize());
        System.out.println("   🖼️ Is image: " + imageMsg.isImageMessage());
        
        // Test audio message
        ChatMessage audioMsg = new ChatMessage(
            "USER001", "Test User",
            "USER002", "Test Receiver",
            "🎤 Test Audio",
            ChatMessage.MessageType.AUDIO,
            "test.wav",
            "/path/to/test.wav",
            "audio/wav",
            512000
        );
        audioMsg.setAudioDuration(30);
        System.out.println("✅ Audio message created: " + audioMsg.getFileName());
        System.out.println("   ⏱️ Duration: " + audioMsg.getFormattedAudioDuration());
        System.out.println("   🎤 Is audio: " + audioMsg.isAudioMessage());
        
        // Test file message
        ChatMessage fileMsg = new ChatMessage(
            "USER001", "Test User",
            "USER002", "Test Receiver",
            "📎 Test File",
            ChatMessage.MessageType.FILE,
            "test.pdf",
            "/path/to/test.pdf",
            "application/pdf",
            2048000
        );
        System.out.println("✅ File message created: " + fileMsg.getFileName());
        System.out.println("   📏 Size: " + fileMsg.getFormattedFileSize());
        System.out.println("   📎 Is file: " + fileMsg.isFileMessage());
        System.out.println("   📁 Has file: " + fileMsg.hasFile());
    }
    
    private static void testFileUploadService() {
        System.out.println("\n📁 Testing FileUploadService class...");
        
        try {
            FileUploadService fileService = new FileUploadService();
            System.out.println("✅ FileUploadService instance created successfully");
            System.out.println("   📁 Upload directories will be initialized");
            
        } catch (Exception e) {
            System.err.println("❌ Error creating FileUploadService: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testAudioRecordingService() {
        System.out.println("\n🎤 Testing AudioRecordingService class...");
        
        try {
            AudioRecordingService audioService = new AudioRecordingService();
            System.out.println("✅ AudioRecordingService instance created successfully");
            
            // Test microphone availability check
            boolean micAvailable = AudioRecordingService.isMicrophoneAvailable();
            System.out.println("   🎤 Microphone available: " + (micAvailable ? "Yes" : "No"));
            
            // Test recording state
            boolean isRecording = audioService.isRecording();
            System.out.println("   📊 Currently recording: " + (isRecording ? "Yes" : "No"));
            
        } catch (Exception e) {
            System.err.println("❌ Error creating AudioRecordingService: " + e.getMessage());
            throw e;
        }
    }
}

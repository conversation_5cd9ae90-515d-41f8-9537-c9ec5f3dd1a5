package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.service.SimpleChatService;

/**
 * Test class to check chat functionality
 */
public class ChatTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 ════════════════════════════════════════════════════════════");
        System.out.println("🧪           KIỂM TRA CHỨC NĂNG CHAT");
        System.out.println("🧪 ════════════════════════════════════════════════════════════");
        
        try {
            // Test UserService
            testUserService();
            
            // Test ChatService
            testChatService();
            
            System.out.println("✅ ════════════════════════════════════════════════════════════");
            System.out.println("✅           TẤT CẢ TEST CHAT THÀNH CÔNG!");
            System.out.println("✅ ════════════════════════════════════════════════════════════");
            
        } catch (Exception e) {
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            System.err.println("❌           LỖI TEST CHAT: " + e.getMessage());
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            e.printStackTrace();
        }
    }
    
    private static void testUserService() {
        System.out.println("\n👥 Testing UserService...");
        
        try {
            UserService userService = new UserService();
            System.out.println("✅ UserService created successfully");
            
            // Test getting all users
            var allUsers = userService.getAllUsers();
            System.out.println("✅ Found " + allUsers.size() + " users in system");
            
            // Count students and teachers
            int studentCount = 0;
            int teacherCount = 0;
            
            for (User user : allUsers) {
                if (user instanceof Student) {
                    studentCount++;
                    Student student = (Student) user;
                    System.out.println("   👨‍🎓 Student: " + 
                        (student.getFullName() != null ? student.getFullName() : "No name") + 
                        " (ID: " + student.getUserId() + ")");
                } else if (user instanceof Teacher) {
                    teacherCount++;
                    Teacher teacher = (Teacher) user;
                    System.out.println("   👨‍🏫 Teacher: " + 
                        (teacher.getFullName() != null ? teacher.getFullName() : "No name") + 
                        " (ID: " + teacher.getUserId() + ")");
                }
            }
            
            System.out.println("📊 Summary: " + studentCount + " students, " + teacherCount + " teachers");
            
        } catch (Exception e) {
            System.err.println("❌ Error testing UserService: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testChatService() {
        System.out.println("\n💬 Testing ChatService...");
        
        try {
            SimpleChatService chatService = new SimpleChatService();
            System.out.println("✅ ChatService created successfully");
            
            // Test getting students for teacher
            System.out.println("\n🔍 Testing getAvailableStudentsForTeacher...");
            var students = chatService.getAvailableStudentsForTeacher("GV001");
            System.out.println("✅ Found " + students.size() + " students for teacher GV001");
            
            for (Student student : students) {
                if (student != null) {
                    System.out.println("   👨‍🎓 " + 
                        (student.getFullName() != null ? student.getFullName() : "No name") + 
                        " - " + 
                        (student.getClassName() != null ? student.getClassName() : "No class"));
                } else {
                    System.out.println("   ⚠️ Found null student");
                }
            }
            
            // Test getting teachers for student
            System.out.println("\n🔍 Testing getAvailableTeachersForStudent...");
            var teachers = chatService.getAvailableTeachersForStudent("SV001");
            System.out.println("✅ Found " + teachers.size() + " teachers for student SV001");
            
            for (Teacher teacher : teachers) {
                if (teacher != null) {
                    System.out.println("   👨‍🏫 " + 
                        (teacher.getFullName() != null ? teacher.getFullName() : "No name") + 
                        " - " + 
                        (teacher.getDepartment() != null ? teacher.getDepartment() : "No department"));
                } else {
                    System.out.println("   ⚠️ Found null teacher");
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error testing ChatService: " + e.getMessage());
            throw e;
        }
    }
}

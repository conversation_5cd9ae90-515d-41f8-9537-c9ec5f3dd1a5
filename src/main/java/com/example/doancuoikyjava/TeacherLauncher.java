// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

/**
 * Teacher Launcher - Direct access to Teacher Dashboard
 */
public class TeacherLauncher extends Application {

    private UserService userService;

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("🔄 Đang khởi tạo UserService cho giáo viên...");
            this.userService = new UserService();
            System.out.println("✅ UserService đã được khởi tạo thành công!");

            // Initialize default teacher if not exists
            System.out.println("🔍 Đang kiểm tra tài khoản Giáo viên mặc định...");
            initializeDefaultTeacher();

            // Get teacher user
            System.out.println("🔐 Đang xác thực tài khoản Giáo viên...");
            User teacher = userService.getUserById("GV001");
            if (teacher == null) {
                System.err.println("❌ ════════════════════════════════════════");
                System.err.println("❌ KHÔNG THỂ TÌM THẤY HOẶC TẠO TÀI KHOẢN GIÁO VIÊN!");
                System.err.println("❌ ════════════════════════════════════════");
                return;
            }

            System.out.println("✅ Xác thực thành công!");
            System.out.println("🎯 Đang thiết lập SceneManager cho giáo viên...");

            // Set up SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            SceneManager.setCurrentUser(teacher);

            System.out.println("🚀 Đang khởi động Teacher Dashboard...");

            // Launch teacher dashboard directly
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml",
                                   "👨‍🏫 TEACHER DASHBOARD - Hệ thống quản lý giảng dạy");

            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("🎉 TEACHER DASHBOARD ĐÃ KHỞI ĐỘNG THÀNH CÔNG!");
            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("👤 Đăng nhập với tư cách: " + teacher.getFullName());
            System.out.println("🏫 Khoa/Bộ môn: Khoa Khoa học máy tính");
            System.out.println("📚 Có thể giảng dạy và quản lý lớp học");
            System.out.println("🎯 Sẵn sàng sử dụng các chức năng giảng dạy!");

        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI KHỞI ĐỘNG TEACHER DASHBOARD!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cấu hình và thử lại!");
            e.printStackTrace();
        }
    }

    private void initializeDefaultTeacher() {
        try {
            if (userService.getUserById("GV001") == null) {
                System.out.println("🆕 Tài khoản Giáo viên chưa tồn tại, đang tạo mới...");

                Teacher teacher = new Teacher();
                teacher.setUserId("GV001");
                teacher.setUsername("teacher");
                teacher.setPassword("teacher123");
                teacher.setFullName("Nguyễn Văn Giáo");
                teacher.setEmail("<EMAIL>");
                teacher.setPhone("0987654321");
                teacher.setRole(User.UserRole.TEACHER);
                teacher.setTeacherId("GV001");
                teacher.setDepartment("Khoa Khoa học máy tính");
                teacher.setPosition("Giảng viên");
                teacher.setQualification("Thạc sĩ Khoa học máy tính");
                teacher.setExperienceYears(5);

                userService.addUser(teacher);
                System.out.println("🎉 ════════════════════════════════════════");
                System.out.println("🎉 TẠO TÀI KHOẢN GIÁO VIÊN THÀNH CÔNG!");
                System.out.println("🎉 ════════════════════════════════════════");
                System.out.println("👤 Tên đăng nhập: teacher");
                System.out.println("🔐 Mật khẩu: teacher123");
                System.out.println("👨‍🏫 Họ tên: Nguyễn Văn Giáo");
                System.out.println("📧 Email: <EMAIL>");
                System.out.println("📱 Điện thoại: 0987654321");
                System.out.println("🏫 Khoa: Khoa Khoa học máy tính");
                System.out.println("💼 Chức vụ: Giảng viên");
                System.out.println("🎓 Trình độ: Thạc sĩ Khoa học máy tính");
                System.out.println("📅 Kinh nghiệm: 5 năm");
            } else {
                System.out.println("✅ Tài khoản Giáo viên đã tồn tại, sẵn sàng đăng nhập!");
            }
        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI TẠO TÀI KHOẢN GIÁO VIÊN MẶC ĐỊNH!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cơ sở dữ liệu!");
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options

        System.out.println("🎓 ════════════════════════════════════════════════════════════");
        System.out.println("🎓           KHỞI ĐỘNG TEACHER DASHBOARD");
        System.out.println("🎓 ════════════════════════════════════════════════════════════");
        System.out.println("👨‍🏫 Chế độ: Teacher Dashboard (Truy cập trực tiếp)");
        System.out.println("👤 Đăng nhập tự động: Giáo viên hệ thống");
        System.out.println("🎯 Quyền truy cập: Các chức năng giảng dạy");
        System.out.println("📚 Có thể thực hiện:");
        System.out.println("   👨‍🎓 • Quản lý Sinh viên trong lớp");
        System.out.println("   📝 • Nhập và chỉnh sửa Điểm số");
        System.out.println("   📅 • Xem và quản lý Lịch giảng dạy");
        System.out.println("   💬 • Chat với Sinh viên");
        System.out.println("   📊 • Xem báo cáo lớp học");
        System.out.println("   📋 • Quản lý Khóa học được phân công");
        System.out.println("🎓 ════════════════════════════════════════════════════════════");
        System.out.println("⏳ Đang khởi động ứng dụng giảng dạy...");

        launch(args);
    }
}
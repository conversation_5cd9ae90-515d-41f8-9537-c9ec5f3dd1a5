package com.example.doancuoikyjava;

public class RunWithJavaFX {
    public static void main(String[] args) {
        // 🚀 Khởi động ứng dụng JavaFX
        System.out.println("🚀 ════════════════════════════════════════════════════════════");
        System.out.println("🎓 HỆ THỐNG QUẢN LÝ SINH VIÊN - JAVAFX APPLICATION");
        System.out.println("🚀 ════════════════════════════════════════════════════════════");
        System.out.println("🔍 Đang kiểm tra JavaFX runtime...");

        // Kiểm tra JavaFX runtime bằng cách thử nạp một class JavaFX
        try {
            Class.forName("javafx.application.Application");
            System.out.println("✅ JavaFX runtime đã được tìm thấy!");
            System.out.println("🎯 Sẵn sàng khởi động ứng dụng...");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            System.err.println("❌ LỖI: Thiếu thành phần JavaFX runtime!");
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            System.err.println("🔧 HƯỚNG DẪN KHẮC PHỤC:");
            System.err.println("➡️  Vui lòng thêm VM options sau khi chạy:");
            System.err.println("📝  --module-path \"đường_dẫn\\tới\\javafx-sdk-XX\\lib\" --add-modules javafx.controls,javafx.fxml");
            System.err.println("📁  Thay \"đường_dẫn\\tới\\javafx-sdk-XX\\lib\" bằng đường dẫn thư mục lib của JavaFX SDK trên máy bạn.");
            System.err.println("💡  Ví dụ với IntelliJ: Vào Run > Edit Configurations > VM options");
            System.err.println("🆘  Liên hệ hỗ trợ nếu vẫn gặp vấn đề!");
            System.err.println("❌ ════════════════════════════════════════════════════════════");
            System.exit(1);
        }

        System.out.println("🎮 ════════════════════════════════════════════════════════════");
        System.out.println("🎮 CHỌN CHỨC NĂNG KHỞI ĐỘNG:");
        System.out.println("🎮 ════════════════════════════════════════════════════════════");
        System.out.println("🔧 AdminLauncher.main(args);     // 👨‍💼 Dashboard Quản trị viên");
        System.out.println("👨‍🏫 TeacherLauncher.main(args);   // 👨‍🏫 Dashboard Giáo viên");
        System.out.println("🎓 StudentLauncher.main(args);   // 👨‍🎓 Dashboard Sinh viên");
        System.out.println("🏠 MainApplication.main(args);   // 🏠 Trang đăng nhập chính");
        System.out.println("🎮 ════════════════════════════════════════════════════════════");

        // Chọn dashboard để chạy (bỏ comment dòng bạn muốn chạy)
        // AdminLauncher.main(args);
        // TeacherLauncher.main(args);
        // StudentLauncher.main(args);

        System.out.println("🏠 Khởi động trang đăng nhập chính...");
        MainApplication.main(args);
    }
}

package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Simple Chat Service with file-based storage fallback
 */
public class SimpleChatService {
    
    private UserService userService;
    private boolean useDatabaseStorage;
    private static final String CHAT_DIR = "data/";
    private static final String CHAT_FILE = CHAT_DIR + "chat_messages.txt";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public SimpleChatService() {
        this.userService = new UserService();
        
        // Check if database is available
        this.useDatabaseStorage = DatabaseConnection.testConnection();
        
        if (!useDatabaseStorage) {
            initializeChatFiles();
            System.out.println("💬 SimpleChatService: Using file-based storage");
        } else {
            System.out.println("💬 SimpleChatService: Database available but using file storage for simplicity");
            initializeChatFiles();
        }
    }
    
    private void initializeChatFiles() {
        try {
            File chatDir = new File(CHAT_DIR);
            if (!chatDir.exists()) {
                chatDir.mkdirs();
            }
            
            File chatFile = new File(CHAT_FILE);
            if (!chatFile.exists()) {
                chatFile.createNewFile();
                System.out.println("✅ Chat file created: " + CHAT_FILE);
            }
        } catch (IOException e) {
            System.err.println("❌ Error initializing chat files: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Save a chat message to file
     */
    public boolean saveMessage(ChatMessage message) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(CHAT_FILE, true))) {
            String line = messageToString(message);
            writer.println(line);
            System.out.println("💾 Message saved to file: " + message.getMessageId());
            return true;
        } catch (IOException e) {
            System.err.println("❌ Error saving message to file: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get chat history between two users from file
     */
    public List<ChatMessage> getChatHistory(String userId1, String userId2) {
        List<ChatMessage> messages = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(CHAT_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                ChatMessage message = parseMessage(line);
                if (message != null) {
                    // Check if message is between the two users
                    if ((message.getSenderId().equals(userId1) && message.getReceiverId().equals(userId2)) ||
                        (message.getSenderId().equals(userId2) && message.getReceiverId().equals(userId1))) {
                        messages.add(message);
                    }
                }
            }
            
            System.out.println("📜 Retrieved " + messages.size() + " messages between " + userId1 + " and " + userId2);
            
        } catch (IOException e) {
            System.err.println("❌ Error reading chat history from file: " + e.getMessage());
            e.printStackTrace();
        }
        
        return messages;
    }
    
    /**
     * Get available teachers for a student to chat with
     */
    public List<Teacher> getAvailableTeachersForStudent(String studentId) {
        List<Teacher> teachers = new ArrayList<>();

        try {
            List<User> allUsers = userService.getAllUsers();

            for (User user : allUsers) {
                if (user instanceof Teacher) {
                    teachers.add((Teacher) user);
                }
            }

            System.out.println("👨‍🏫 Found " + teachers.size() + " available teachers for student " + studentId);

        } catch (Exception e) {
            System.err.println("❌ Error getting available teachers: " + e.getMessage());
            e.printStackTrace();
        }

        return teachers;
    }
    
    /**
     * Get available students for a teacher to chat with (based on enrolled courses)
     */
    public List<Student> getAvailableStudentsForTeacher(String teacherId) {
        List<Student> availableStudents = new ArrayList<>();

        try {
            // For SimpleChatService, we'll use a simplified approach
            // Check if students are enrolled in any courses taught by this teacher
            CourseService courseService = new CourseService();
            List<Course> teacherCourses = courseService.getCoursesByTeacher(teacherId);

            if (teacherCourses == null || teacherCourses.isEmpty()) {
                System.out.println("⚠️ Teacher " + teacherId + " has no courses assigned");
                return availableStudents;
            }

            // Get course IDs that this teacher teaches
            java.util.Set<String> teacherCourseIds = teacherCourses.stream()
                    .filter(course -> course != null && course.getCourseId() != null)
                    .map(Course::getCourseId)
                    .collect(java.util.stream.Collectors.toSet());

            System.out.println("📚 Teacher " + teacherId + " teaches courses: " + teacherCourseIds);

            // Get all students
            List<User> allUsers = userService.getAllUsers();

            for (User user : allUsers) {
                if (user instanceof Student) {
                    Student student = (Student) user;

                    // Check if student is enrolled in any of teacher's courses
                    if (student.getEnrolledCourses() != null && !student.getEnrolledCourses().isEmpty()) {
                        boolean isEnrolledInTeacherCourse = student.getEnrolledCourses().stream()
                                .anyMatch(courseId -> teacherCourseIds.contains(courseId));

                        if (isEnrolledInTeacherCourse) {
                            availableStudents.add(student);
                            System.out.println("✅ Student " + student.getStudentId() + " (" + student.getFullName() + ") is enrolled in teacher's courses");
                        } else {
                            System.out.println("❌ Student " + student.getStudentId() + " (" + student.getFullName() + ") is not enrolled in teacher's courses");
                        }
                    } else {
                        System.out.println("❌ Student " + student.getStudentId() + " (" + student.getFullName() + ") has no enrolled courses");
                    }
                }
            }

            System.out.println("👨‍🎓 Found " + availableStudents.size() + " students enrolled in teacher's courses for teacher " + teacherId);

        } catch (Exception e) {
            System.err.println("❌ Error getting available students for teacher: " + e.getMessage());
            e.printStackTrace();
        }

        return availableStudents;
    }
    
    /**
     * Convert ChatMessage to string for file storage
     */
    private String messageToString(ChatMessage message) {
        return String.join("|",
            message.getMessageId(),
            message.getSenderId(),
            message.getSenderName(),
            message.getReceiverId(),
            message.getReceiverName(),
            message.getContent(),
            message.getType().name(),
            message.getTimestamp().format(FORMATTER),
            String.valueOf(message.isRead()),
            message.getCourseId() != null ? message.getCourseId() : "",
            message.getCourseName() != null ? message.getCourseName() : "",
            // File information
            message.getFileName() != null ? message.getFileName() : "",
            message.getFilePath() != null ? message.getFilePath() : "",
            message.getFileType() != null ? message.getFileType() : "",
            String.valueOf(message.getFileSize()),
            message.getThumbnailPath() != null ? message.getThumbnailPath() : "",
            String.valueOf(message.getAudioDuration())
        );
    }
    
    /**
     * Parse string to ChatMessage
     */
    private ChatMessage parseMessage(String line) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        try {
            String[] parts = line.split("\\|");
            if (parts.length < 9) {
                return null;
            }

            ChatMessage message = new ChatMessage();
            message.setMessageId(parts[0]);
            message.setSenderId(parts[1]);
            message.setSenderName(parts[2]);
            message.setReceiverId(parts[3]);
            message.setReceiverName(parts[4]);
            message.setContent(parts[5]);
            message.setType(ChatMessage.MessageType.valueOf(parts[6]));
            message.setTimestamp(LocalDateTime.parse(parts[7], FORMATTER));
            message.setRead(Boolean.parseBoolean(parts[8]));

            if (parts.length > 9 && !parts[9].isEmpty()) {
                message.setCourseId(parts[9]);
            }
            if (parts.length > 10 && !parts[10].isEmpty()) {
                message.setCourseName(parts[10]);
            }

            // Parse file information if available
            if (parts.length > 11 && !parts[11].isEmpty()) {
                message.setFileName(parts[11]);
            }
            if (parts.length > 12 && !parts[12].isEmpty()) {
                message.setFilePath(parts[12]);
            }
            if (parts.length > 13 && !parts[13].isEmpty()) {
                message.setFileType(parts[13]);
            }
            if (parts.length > 14 && !parts[14].isEmpty()) {
                try {
                    message.setFileSize(Long.parseLong(parts[14]));
                } catch (NumberFormatException e) {
                    message.setFileSize(0);
                }
            }
            if (parts.length > 15 && !parts[15].isEmpty()) {
                message.setThumbnailPath(parts[15]);
            }
            if (parts.length > 16 && !parts[16].isEmpty()) {
                try {
                    message.setAudioDuration(Integer.parseInt(parts[16]));
                } catch (NumberFormatException e) {
                    message.setAudioDuration(0);
                }
            }

            return message;

        } catch (Exception e) {
            System.err.println("❌ Error parsing message: " + line + " - " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Mark a message as read (simplified - just return true for file storage)
     */
    public boolean markMessageAsRead(String messageId) {
        // For file storage, this would require rewriting the entire file
        // For simplicity, we'll just return true
        return true;
    }
    
    /**
     * Get unread message count (simplified - return 0 for file storage)
     */
    public int getUnreadMessageCount(String userId) {
        // For file storage, this would require reading the entire file
        // For simplicity, we'll just return 0
        return 0;
    }

    /**
     * Send a chat message (wrapper for saveMessage)
     */
    public boolean sendMessage(ChatMessage message) {
        try {
            // Set default values if not set
            if (message.getMessageId() == null || message.getMessageId().isEmpty()) {
                message.setMessageId(java.util.UUID.randomUUID().toString());
            }

            if (message.getTimestamp() == null) {
                message.setTimestamp(java.time.LocalDateTime.now());
            }

            // Save the message
            boolean success = saveMessage(message);

            if (success) {
                System.out.println("📤 Message sent successfully: " + message.getMessageId());
            } else {
                System.err.println("❌ Failed to send message: " + message.getMessageId());
            }

            return success;

        } catch (Exception e) {
            System.err.println("❌ Error sending message: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get available teachers for a teacher to chat with (based on teaching same courses)
     */
    public List<Teacher> getAvailableTeachersForTeacher(String teacherId) {
        List<Teacher> availableTeachers = new ArrayList<>();

        try {
            // For SimpleChatService, we'll use a simplified approach
            // In a real implementation, this would check course assignments
            List<User> allUsers = userService.getAllUsers();

            for (User user : allUsers) {
                if (user instanceof Teacher && !user.getUserId().equals(teacherId)) {
                    availableTeachers.add((Teacher) user);
                }
            }

            System.out.println("👨‍🏫 Found " + availableTeachers.size() + " available colleague teachers for teacher " + teacherId);

        } catch (Exception e) {
            System.err.println("❌ Error getting available teachers for teacher: " + e.getMessage());
            e.printStackTrace();
        }

        return availableTeachers;
    }
}

package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class ExcelExportService {
    
    private static final String EXPORT_DIRECTORY = "exports";
    
    /**
     * Export danh sách sinh viên ra Excel
     */
    public String exportStudents(List<Student> students) {
        try {
            System.out.println("📊 Starting export of " + students.size() + " students to Excel...");
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Danh sách Sinh viên");
            
            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "STT", "Mã SV", "Họ và tên", "Username", "Email", 
                "Số điện thoại", "Địa chỉ", "Ngày sinh", "Lớp", "Chuyên ngành", 
                "Năm học", "GPA", "Ngày tạo"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Student student : students) {
                if (student == null) continue;
                
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                
                // STT
                createCell(row, colNum++, rowNum - 1, dataStyle);
                // Mã SV
                createCell(row, colNum++, student.getStudentId(), dataStyle);
                // Họ và tên
                createCell(row, colNum++, student.getFullName(), dataStyle);
                // Username
                createCell(row, colNum++, student.getUsername(), dataStyle);
                // Email
                createCell(row, colNum++, student.getEmail(), dataStyle);
                // Số điện thoại
                createCell(row, colNum++, student.getPhoneNumber(), dataStyle);
                // Địa chỉ
                createCell(row, colNum++, student.getAddress(), dataStyle);
                // Ngày sinh
                createCell(row, colNum++, student.getDateOfBirth() != null ? 
                    student.getDateOfBirth().toString() : "", dataStyle);
                // Lớp
                createCell(row, colNum++, student.getClassName(), dataStyle);
                // Chuyên ngành
                createCell(row, colNum++, student.getMajor(), dataStyle);
                // Năm học
                createCell(row, colNum++, student.getYearOfStudy(), dataStyle);
                // GPA
                createCell(row, colNum++, student.getGpa(), dataStyle);
                // Ngày tạo
                createCell(row, colNum++, student.getCreatedAt() != null ? 
                    student.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "", dataStyle);
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Save file
            String fileName = generateFileName("DanhSachSinhVien");
            String filePath = saveWorkbook(workbook, fileName);
            
            workbook.close();
            
            System.out.println("✅ Successfully exported " + students.size() + " students to: " + filePath);
            return filePath;
            
        } catch (Exception e) {
            System.err.println("❌ Error exporting students to Excel: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Export danh sách giáo viên ra Excel
     */
    public String exportTeachers(List<Teacher> teachers) {
        try {
            System.out.println("📊 Starting export of " + teachers.size() + " teachers to Excel...");
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Danh sách Giáo viên");
            
            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "STT", "Mã GV", "Họ và tên", "Username", "Email", 
                "Số điện thoại", "Địa chỉ", "Ngày sinh", "Khoa", "Chức vụ", 
                "Lương", "Trình độ", "Kinh nghiệm", "Ngày tạo"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Teacher teacher : teachers) {
                if (teacher == null) continue;
                
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                
                // STT
                createCell(row, colNum++, rowNum - 1, dataStyle);
                // Mã GV
                createCell(row, colNum++, teacher.getTeacherId(), dataStyle);
                // Họ và tên
                createCell(row, colNum++, teacher.getFullName(), dataStyle);
                // Username
                createCell(row, colNum++, teacher.getUsername(), dataStyle);
                // Email
                createCell(row, colNum++, teacher.getEmail(), dataStyle);
                // Số điện thoại
                createCell(row, colNum++, teacher.getPhoneNumber(), dataStyle);
                // Địa chỉ
                createCell(row, colNum++, teacher.getAddress(), dataStyle);
                // Ngày sinh
                createCell(row, colNum++, teacher.getDateOfBirth() != null ? 
                    teacher.getDateOfBirth().toString() : "", dataStyle);
                // Khoa
                createCell(row, colNum++, teacher.getDepartment(), dataStyle);
                // Chức vụ
                createCell(row, colNum++, teacher.getPosition(), dataStyle);
                // Lương
                createCell(row, colNum++, teacher.getSalary(), dataStyle);
                // Trình độ
                createCell(row, colNum++, teacher.getQualification(), dataStyle);
                // Kinh nghiệm
                createCell(row, colNum++, teacher.getExperienceYears(), dataStyle);
                // Ngày tạo
                createCell(row, colNum++, teacher.getCreatedAt() != null ? 
                    teacher.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "", dataStyle);
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Save file
            String fileName = generateFileName("DanhSachGiaoVien");
            String filePath = saveWorkbook(workbook, fileName);
            
            workbook.close();
            
            System.out.println("✅ Successfully exported " + teachers.size() + " teachers to: " + filePath);
            return filePath;
            
        } catch (Exception e) {
            System.err.println("❌ Error exporting teachers to Excel: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Export danh sách môn học ra Excel
     */
    public String exportCourses(List<Course> courses) {
        try {
            System.out.println("📊 Starting export of " + courses.size() + " courses to Excel...");
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Danh sách Môn học");
            
            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "STT", "Mã môn", "Tên môn học", "Mô tả", "Số tín chỉ", 
                "Mã giáo viên", "Tên giáo viên", "Số sinh viên", "Ngày tạo"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Course course : courses) {
                if (course == null) continue;
                
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                
                // STT
                createCell(row, colNum++, rowNum - 1, dataStyle);
                // Mã môn
                createCell(row, colNum++, course.getCourseId(), dataStyle);
                // Tên môn học
                createCell(row, colNum++, course.getCourseName(), dataStyle);
                // Mô tả
                createCell(row, colNum++, course.getDescription(), dataStyle);
                // Số tín chỉ
                createCell(row, colNum++, course.getCredits(), dataStyle);
                // Mã giáo viên
                createCell(row, colNum++, course.getTeacherId(), dataStyle);
                // Tên giáo viên
                createCell(row, colNum++, course.getTeacherName(), dataStyle);
                // Số sinh viên
                createCell(row, colNum++, course.getEnrolledStudents() != null ? 
                    course.getEnrolledStudents().size() : 0, dataStyle);
                // Ngày tạo
                createCell(row, colNum++, course.getCreatedAt() != null ? 
                    course.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "", dataStyle);
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Save file
            String fileName = generateFileName("DanhSachMonHoc");
            String filePath = saveWorkbook(workbook, fileName);
            
            workbook.close();
            
            System.out.println("✅ Successfully exported " + courses.size() + " courses to: " + filePath);
            return filePath;
            
        } catch (Exception e) {
            System.err.println("❌ Error exporting courses to Excel: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.util.DataManager;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.time.LocalDate;
import java.util.List;

public class UserService {

    private DatabaseUserService databaseUserService;
    private boolean useDatabaseStorage;

    public UserService() {
        // Kiểm tra xem có thể sử dụng database không
        useDatabaseStorage = DatabaseConnection.testConnection();
        if (useDatabaseStorage) {
            databaseUserService = new DatabaseUserService();
            System.out.println("📊 UserService: Sử dụng SQL Server Database");
        } else {
            System.out.println("📁 UserService: Sử dụng File-based Storage");
        }
    }
    
    public User authenticate(String username, String password, User.UserRole role) {
        if (useDatabaseStorage) {
            return databaseUserService.authenticate(username, password, role);
        } else {
            // Fallback to file-based storage
            List<User> users = DataManager.loadUsers();

            return users.stream()
                    .filter(user -> user.getUsername().equals(username) &&
                                   user.getPassword().equals(password) &&
                                   user.getRole() == role)
                    .findFirst()
                    .orElse(null);
        }
    }
    
    public List<User> getAllUsers() {
        return DataManager.loadUsers();
    }
    
    public List<User> getUsersByRole(User.UserRole role) {
        if (useDatabaseStorage) {
            return databaseUserService.getUsersByRole(role);
        } else {
            return DataManager.loadUsers().stream()
                    .filter(user -> user.getRole() == role)
                    .toList();
        }
    }

    public User getUserById(String userId) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                System.err.println("⚠️ User ID is null or empty");
                return null;
            }

            if (useDatabaseStorage) {
                return databaseUserService.getUserById(userId);
            } else {
                List<User> users = DataManager.loadUsers();
                if (users == null) {
                    System.err.println("⚠️ Users list is null");
                    return null;
                }

                return users.stream()
                        .filter(user -> user != null &&
                                       user.getUserId() != null &&
                                       user.getUserId().equals(userId))
                        .findFirst()
                        .orElse(null);
            }
        } catch (Exception e) {
            System.err.println("❌ Error getting user by ID: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    public boolean addUser(User user) {
        if (useDatabaseStorage) {
            return databaseUserService.addUser(user);
        } else {
            List<User> users = DataManager.loadUsers();

            // Check if username already exists
            boolean usernameExists = users.stream()
                    .anyMatch(u -> u.getUsername().equals(user.getUsername()));

            if (usernameExists) {
                return false;
            }

            users.add(user);
            DataManager.saveUsers(users);
            return true;
        }
    }

    public boolean updateUser(User updatedUser) {
        if (useDatabaseStorage) {
            return databaseUserService.updateUser(updatedUser);
        } else {
            List<User> users = DataManager.loadUsers();

            for (int i = 0; i < users.size(); i++) {
                if (users.get(i).getUserId().equals(updatedUser.getUserId())) {
                    users.set(i, updatedUser);
                    DataManager.saveUsers(users);
                    return true;
                }
            }
            return false;
        }
    }

    public boolean deleteUser(String userId) {
        if (useDatabaseStorage) {
            return databaseUserService.deleteUser(userId);
        } else {
            List<User> users = DataManager.loadUsers();
            boolean removed = users.removeIf(user -> user.getUserId().equals(userId));

            if (removed) {
                DataManager.saveUsers(users);
            }
            return removed;
        }
    }
    
    public String generateNextUserId(User.UserRole role) {
        if (useDatabaseStorage) {
            return databaseUserService.generateNextUserId(role);
        } else {
            List<User> users = DataManager.loadUsers();
            String prefix;

            switch (role) {
                case ADMIN:
                    prefix = "ADM";
                    break;
                case TEACHER:
                    prefix = "TCH";
                    break;
                case STUDENT:
                    prefix = "STU";
                    break;
                default:
                    prefix = "USR";
            }

            int maxNumber = users.stream()
                    .filter(user -> user.getUserId().startsWith(prefix))
                    .mapToInt(user -> {
                        try {
                            return Integer.parseInt(user.getUserId().substring(3));
                        } catch (NumberFormatException e) {
                            return 0;
                        }
                    })
                    .max()
                    .orElse(0);

            return prefix + String.format("%03d", maxNumber + 1);
        }
    }
    
    public long getTotalStudents() {
        return getUsersByRole(User.UserRole.STUDENT).size();
    }
    
    public long getTotalTeachers() {
        return getUsersByRole(User.UserRole.TEACHER).size();
    }

    /**
     * Getter để kiểm tra xem có đang sử dụng database storage không
     */
    public boolean isUsingDatabaseStorage() {
        return useDatabaseStorage;
    }

    /**
     * Kiểm tra username đã tồn tại
     */
    public boolean isUsernameExists(String username) {
        if (useDatabaseStorage) {
            return databaseUserService.isUsernameExists(username);
        } else {
            List<User> users = DataManager.loadUsers();
            return users.stream().anyMatch(user -> user.getUsername().equals(username));
        }
    }

    /**
     * Kiểm tra user ID đã tồn tại
     */
    public boolean isUserIdExists(String userId) {
        if (useDatabaseStorage) {
            return databaseUserService.isUserIdExists(userId);
        } else {
            List<User> users = DataManager.loadUsers();
            return users.stream().anyMatch(user -> user.getUserId().equals(userId));
        }
    }
}

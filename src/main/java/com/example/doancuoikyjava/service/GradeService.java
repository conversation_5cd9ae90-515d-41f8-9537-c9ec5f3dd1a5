package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.Grade;
import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.util.DataManager;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.io.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class GradeService {
    private static final String GRADES_FILE = "data/grades.txt";

    private DatabaseGradeService databaseGradeService;
    private boolean useDatabaseStorage;

    public GradeService() {
        useDatabaseStorage = DatabaseConnection.testConnection();
        if (useDatabaseStorage) {
            databaseGradeService = new DatabaseGradeService();
            System.out.println("📊 GradeService: Sử dụng SQL Server Database");
        } else {
            System.out.println("📁 GradeService: Sử dụng File-based Storage");
        }
    }
    
    public List<Grade> getAllGrades() {
        if (useDatabaseStorage) {
            return databaseGradeService.getAllGrades();
        } else {
            return loadGrades();
        }
    }

    public List<Grade> getGradesByStudent(String studentId) {
        if (useDatabaseStorage) {
            return databaseGradeService.getGradesByStudent(studentId);
        } else {
            return loadGrades().stream()
                    .filter(grade -> grade.getStudentId().equals(studentId))
                    .collect(Collectors.toList());
        }
    }

    public List<Grade> getGradesByCourse(String courseId) {
        if (useDatabaseStorage) {
            return databaseGradeService.getGradesByCourse(courseId);
        } else {
            return loadGrades().stream()
                    .filter(grade -> grade.getCourseId().equals(courseId))
                    .collect(Collectors.toList());
        }
    }

    public List<Grade> getGradesByTeacher(String teacherId) {
        try {
            if (teacherId == null) {
                System.err.println("⚠️ Teacher ID is null");
                return new ArrayList<>();
            }

            if (useDatabaseStorage) {
                List<Grade> grades = databaseGradeService.getGradesByTeacher(teacherId);
                return grades != null ? grades.stream()
                        .filter(grade -> grade != null && isValidGrade(grade))
                        .collect(Collectors.toList()) : new ArrayList<>();
            } else {
                return loadGrades().stream()
                        .filter(grade -> grade != null &&
                                       grade.getTeacherId() != null &&
                                       grade.getTeacherId().equals(teacherId) &&
                                       isValidGrade(grade))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            System.err.println("❌ Error getting grades by teacher: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    private boolean isValidGrade(Grade grade) {
        return grade != null &&
               grade.getStudentId() != null &&
               grade.getCourseId() != null &&
               grade.getCourseName() != null &&
               grade.getSemester() != null &&
               grade.getTeacherId() != null;
    }
    
    public boolean addGrade(Grade grade) {
        if (useDatabaseStorage) {
            return databaseGradeService.addGrade(grade);
        } else {
            List<Grade> grades = loadGrades();

            // Check if grade already exists for this student and course
            boolean exists = grades.stream()
                    .anyMatch(g -> g.getStudentId().equals(grade.getStudentId()) &&
                                  g.getCourseId().equals(grade.getCourseId()) &&
                                  g.getSemester().equals(grade.getSemester()));

            if (exists) {
                return false; // Grade already exists
            }

            // Generate grade ID
            String gradeId = generateNextGradeId();
            grade.setGradeId(gradeId);
            grade.setDateRecorded(LocalDate.now());

            grades.add(grade);
            saveGrades(grades);

            // Update student GPA
            updateStudentGPA(grade.getStudentId());

            return true;
        }
    }
    
    public boolean updateGrade(Grade updatedGrade) {
        if (useDatabaseStorage) {
            return databaseGradeService.updateGrade(updatedGrade);
        } else {
            List<Grade> grades = loadGrades();

            for (int i = 0; i < grades.size(); i++) {
                if (grades.get(i).getGradeId().equals(updatedGrade.getGradeId())) {
                    grades.set(i, updatedGrade);
                    saveGrades(grades);

                    // Update student GPA
                    updateStudentGPA(updatedGrade.getStudentId());
                    return true;
                }
            }
            return false;
        }
    }

    public boolean deleteGrade(String gradeId) {
        if (useDatabaseStorage) {
            return databaseGradeService.deleteGrade(gradeId);
        } else {
            List<Grade> grades = loadGrades();
            Grade gradeToDelete = grades.stream()
                    .filter(g -> g.getGradeId().equals(gradeId))
                    .findFirst()
                    .orElse(null);

            boolean removed = grades.removeIf(grade -> grade.getGradeId().equals(gradeId));

            if (removed) {
                saveGrades(grades);
                if (gradeToDelete != null) {
                    updateStudentGPA(gradeToDelete.getStudentId());
                }
            }
            return removed;
        }
    }
    
    private void updateStudentGPA(String studentId) {
        UserService userService = new UserService();
        Student student = (Student) userService.getUserById(studentId);
        
        if (student != null) {
            List<Grade> studentGrades = getGradesByStudent(studentId);
            student.setGrades(studentGrades);
            
            // Calculate GPA
            if (!studentGrades.isEmpty()) {
                double totalPoints = 0.0;
                int totalCredits = 0;
                
                for (Grade grade : studentGrades) {
                    totalPoints += grade.getGradePoint() * grade.getCredits();
                    totalCredits += grade.getCredits();
                }
                
                double gpa = totalCredits > 0 ? totalPoints / totalCredits : 0.0;
                student.setGpa(gpa);
            } else {
                student.setGpa(0.0);
            }
            
            userService.updateUser(student);
        }
    }
    
    private String generateNextGradeId() {
        List<Grade> grades = loadGrades();
        
        int maxNumber = grades.stream()
                .filter(grade -> grade.getGradeId().startsWith("GRD"))
                .mapToInt(grade -> {
                    try {
                        return Integer.parseInt(grade.getGradeId().substring(3));
                    } catch (NumberFormatException e) {
                        return 0;
                    }
                })
                .max()
                .orElse(0);
        
        return "GRD" + String.format("%03d", maxNumber + 1);
    }
    
    private List<Grade> loadGrades() {
        List<Grade> grades = new ArrayList<>();
        File file = new File(GRADES_FILE);
        
        if (!file.exists()) {
            return grades;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Grade grade = parseGrade(line);
                if (grade != null) {
                    grades.add(grade);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return grades;
    }
    
    private void saveGrades(List<Grade> grades) {
        File file = new File(GRADES_FILE);
        file.getParentFile().mkdirs();
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            for (Grade grade : grades) {
                writer.println(gradeToString(grade));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    private Grade parseGrade(String line) {
        String[] parts = line.split("\\|");
        if (parts.length < 9) return null;
        
        Grade grade = new Grade();
        grade.setGradeId(parts[0]);
        grade.setStudentId(parts[1]);
        grade.setCourseId(parts[2]);
        grade.setCourseName(parts[3]);
        grade.setScore(Double.parseDouble(parts[4]));
        grade.setCredits(Integer.parseInt(parts[5]));
        grade.setSemester(parts[6]);
        grade.setTeacherId(parts[7]);
        grade.setDateRecorded(LocalDate.parse(parts[8]));
        
        return grade;
    }
    
    private String gradeToString(Grade grade) {
        return grade.getGradeId() + "|" +
               grade.getStudentId() + "|" +
               grade.getCourseId() + "|" +
               grade.getCourseName() + "|" +
               grade.getScore() + "|" +
               grade.getCredits() + "|" +
               grade.getSemester() + "|" +
               grade.getTeacherId() + "|" +
               grade.getDateRecorded();
    }
    
    public double getAverageGradeByCourse(String courseId) {
        if (useDatabaseStorage) {
            return databaseGradeService.getAverageGradeByCourse(courseId);
        } else {
            List<Grade> courseGrades = getGradesByCourse(courseId);
            if (courseGrades.isEmpty()) {
                return 0.0;
            }

            return courseGrades.stream()
                    .mapToDouble(Grade::getScore)
                    .average()
                    .orElse(0.0);
        }
    }

    public long getTotalGrades() {
        if (useDatabaseStorage) {
            return databaseGradeService.getTotalGrades();
        } else {
            return loadGrades().size();
        }
    }
}

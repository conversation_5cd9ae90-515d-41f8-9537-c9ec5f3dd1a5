package com.example.doancuoikyjava.service;

import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.geometry.Pos;

import java.io.File;

/**
 * Service for previewing attachments before sending
 */
public class AttachmentPreviewService {
    
    private FileDownloadService fileDownloadService;
    
    public AttachmentPreviewService() {
        this.fileDownloadService = new FileDownloadService();
    }
    
    /**
     * Create preview for image attachment
     */
    public VBox createImagePreview(File imageFile, Runnable onRemove) {
        VBox previewBox = new VBox(5);
        previewBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 10; -fx-background-radius: 10; -fx-padding: 10;");
        
        try {
            // Image preview
            Image image = new Image(imageFile.toURI().toString());
            ImageView imageView = new ImageView(image);
            imageView.setFitWidth(150);
            imageView.setFitHeight(100);
            imageView.setPreserveRatio(true);
            imageView.setSmooth(true);
            imageView.setStyle("-fx-border-color: #ced4da; -fx-border-radius: 5;");
            
            // File info
            HBox infoBox = new HBox(10);
            infoBox.setAlignment(Pos.CENTER_LEFT);
            
            Label iconLabel = new Label("🖼️");
            iconLabel.setFont(Font.font(16));
            
            VBox fileInfo = new VBox(2);
            Label nameLabel = new Label(imageFile.getName());
            nameLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
            
            Label sizeLabel = new Label(formatFileSize(imageFile.length()));
            sizeLabel.setFont(Font.font("System", 10));
            sizeLabel.setTextFill(Color.GRAY);
            
            fileInfo.getChildren().addAll(nameLabel, sizeLabel);
            
            // Remove button
            Button removeBtn = new Button("❌");
            removeBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 5 8; -fx-font-size: 10px; -fx-cursor: hand;");
            removeBtn.setTooltip(new Tooltip("Xóa ảnh đính kèm"));
            removeBtn.setOnAction(event -> onRemove.run());

            infoBox.getChildren().addAll(iconLabel, fileInfo, removeBtn);

            previewBox.getChildren().addAll(imageView, infoBox);

        } catch (Exception ex) {
            // Error preview
            Label errorLabel = new Label("❌ Không thể xem trước ảnh");
            errorLabel.setTextFill(Color.RED);
            errorLabel.setFont(Font.font("System", FontWeight.BOLD, 12));

            Button removeBtn = new Button("❌ Xóa");
            removeBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-border-radius: 10; -fx-background-radius: 10; -fx-padding: 5 10; -fx-cursor: hand;");
            removeBtn.setOnAction(event -> onRemove.run());
            
            HBox errorBox = new HBox(10);
            errorBox.setAlignment(Pos.CENTER_LEFT);
            errorBox.getChildren().addAll(errorLabel, removeBtn);
            
            previewBox.getChildren().add(errorBox);
        }
        
        return previewBox;
    }
    
    /**
     * Create preview for file attachment
     */
    public VBox createFilePreview(File file, Runnable onRemove) {
        VBox previewBox = new VBox(5);
        previewBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 10; -fx-background-radius: 10; -fx-padding: 10;");
        
        // File info
        HBox infoBox = new HBox(10);
        infoBox.setAlignment(Pos.CENTER_LEFT);
        
        String fileIcon = fileDownloadService.getFileIcon(file.getName());
        Label iconLabel = new Label(fileIcon);
        iconLabel.setFont(Font.font(24));
        
        VBox fileInfo = new VBox(2);
        Label nameLabel = new Label(file.getName());
        nameLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
        nameLabel.setWrapText(true);
        nameLabel.setMaxWidth(200);
        
        Label sizeLabel = new Label(formatFileSize(file.length()));
        sizeLabel.setFont(Font.font("System", 10));
        sizeLabel.setTextFill(Color.GRAY);
        
        Label typeLabel = new Label(getFileType(file.getName()));
        typeLabel.setFont(Font.font("System", 10));
        typeLabel.setTextFill(Color.BLUE);
        
        fileInfo.getChildren().addAll(nameLabel, sizeLabel, typeLabel);
        
        // Remove button
        Button removeBtn = new Button("❌");
        removeBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 5 8; -fx-font-size: 10px; -fx-cursor: hand;");
        removeBtn.setTooltip(new Tooltip("Xóa file đính kèm"));
        removeBtn.setOnAction(e -> onRemove.run());
        
        infoBox.getChildren().addAll(iconLabel, fileInfo, removeBtn);
        previewBox.getChildren().add(infoBox);
        
        return previewBox;
    }
    
    /**
     * Create compact preview for message input area
     */
    public HBox createCompactPreview(File file, boolean isImage, Runnable onRemove) {
        HBox compactBox = new HBox(8);
        compactBox.setAlignment(Pos.CENTER_LEFT);
        compactBox.setStyle("-fx-background-color: #e3f2fd; -fx-border-color: #2196f3; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 5 10;");
        
        // Icon
        String icon = isImage ? "🖼️" : fileDownloadService.getFileIcon(file.getName());
        Label iconLabel = new Label(icon);
        iconLabel.setFont(Font.font(14));
        
        // File name (truncated)
        String fileName = file.getName();
        if (fileName.length() > 20) {
            fileName = fileName.substring(0, 17) + "...";
        }
        Label nameLabel = new Label(fileName);
        nameLabel.setFont(Font.font("System", FontWeight.BOLD, 11));
        nameLabel.setTextFill(Color.DARKBLUE);
        
        // Remove button
        Button removeBtn = new Button("×");
        removeBtn.setStyle("-fx-background-color: #f44336; -fx-text-fill: white; -fx-border-radius: 10; -fx-background-radius: 10; -fx-padding: 2 6; -fx-font-size: 12px; -fx-cursor: hand;");
        removeBtn.setTooltip(new Tooltip("Xóa đính kèm"));
        removeBtn.setOnAction(e -> onRemove.run());
        
        compactBox.getChildren().addAll(iconLabel, nameLabel, removeBtn);
        
        return compactBox;
    }
    
    /**
     * Validate if file can be attached
     */
    public AttachmentValidation validateAttachment(File file, boolean isImage) {
        if (file == null || !file.exists()) {
            return new AttachmentValidation(false, "File không tồn tại");
        }
        
        if (!file.canRead()) {
            return new AttachmentValidation(false, "Không thể đọc file");
        }
        
        // Size limits
        long maxSize = isImage ? 10 * 1024 * 1024 : 20 * 1024 * 1024; // 10MB for images, 20MB for files
        if (file.length() > maxSize) {
            String limit = isImage ? "10MB" : "20MB";
            return new AttachmentValidation(false, "File quá lớn. Giới hạn: " + limit);
        }
        
        if (file.length() == 0) {
            return new AttachmentValidation(false, "File rỗng");
        }
        
        // File type validation
        if (isImage) {
            String extension = getFileExtension(file.getName()).toLowerCase();
            if (!isImageExtension(extension)) {
                return new AttachmentValidation(false, "Định dạng ảnh không hỗ trợ. Chỉ hỗ trợ: JPG, PNG, GIF, BMP, WebP");
            }
        }
        
        return new AttachmentValidation(true, "File hợp lệ");
    }
    
    /**
     * Format file size
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * Get file type description
     */
    private String getFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        switch (extension) {
            case "pdf": return "PDF Document";
            case "doc":
            case "docx": return "Word Document";
            case "xls":
            case "xlsx": return "Excel Spreadsheet";
            case "ppt":
            case "pptx": return "PowerPoint Presentation";
            case "txt": return "Text File";
            case "jpg":
            case "jpeg": return "JPEG Image";
            case "png": return "PNG Image";
            case "gif": return "GIF Image";
            case "bmp": return "Bitmap Image";
            case "webp": return "WebP Image";
            default: return extension.toUpperCase() + " File";
        }
    }
    
    /**
     * Get file extension
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot + 1) : "";
    }
    
    /**
     * Check if extension is image
     */
    private boolean isImageExtension(String extension) {
        return extension.equals("jpg") || extension.equals("jpeg") || 
               extension.equals("png") || extension.equals("gif") || 
               extension.equals("bmp") || extension.equals("webp");
    }
    
    /**
     * Attachment validation result
     */
    public static class AttachmentValidation {
        private final boolean valid;
        private final String message;
        
        public AttachmentValidation(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
    }
}

package com.example.doancuoikyjava.service;

import javax.sound.sampled.*;
import java.io.*;

/**
 * Service for playing audio messages in chat
 */
public class AudioPlayerService {
    
    private Clip currentClip;
    private boolean isPlaying = false;
    private String currentAudioFile;
    private AudioPlayerListener listener;
    
    public interface AudioPlayerListener {
        void onPlaybackStarted(String fileName);
        void onPlaybackStopped(String fileName);
        void onPlaybackCompleted(String fileName);
        void onPlaybackError(String fileName, String error);
    }
    
    public AudioPlayerService() {
        // Initialize
    }
    
    /**
     * Set listener for playback events
     */
    public void setListener(AudioPlayerListener listener) {
        this.listener = listener;
    }
    
    /**
     * Play audio file
     */
    public PlaybackResult playAudio(String audioFilePath, String fileName) {
        try {
            // Stop current playback if any
            if (isPlaying) {
                stopAudio();
            }
            
            File audioFile = new File(audioFilePath);
            if (!audioFile.exists()) {
                String error = "❌ File audio không tồn tại: " + fileName;
                if (listener != null) listener.onPlaybackError(fileName, error);
                return new PlaybackResult(false, error);
            }
            
            // Load audio file
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioFile);
            currentClip = AudioSystem.getClip();
            currentClip.open(audioInputStream);
            
            // Add line listener for completion
            currentClip.addLineListener(event -> {
                if (event.getType() == LineEvent.Type.STOP) {
                    if (isPlaying) { // Only if we were actually playing (not manually stopped)
                        isPlaying = false;
                        currentAudioFile = null;
                        if (listener != null) listener.onPlaybackCompleted(fileName);
                        System.out.println("🎵 Audio playback completed: " + fileName);
                    }
                }
            });
            
            // Start playback
            currentClip.start();
            isPlaying = true;
            currentAudioFile = fileName;
            
            if (listener != null) listener.onPlaybackStarted(fileName);
            System.out.println("🎵 Audio playback started: " + fileName);
            
            return new PlaybackResult(true, "🎵 Đang phát: " + fileName);
            
        } catch (UnsupportedAudioFileException e) {
            String error = "❌ Định dạng audio không được hỗ trợ: " + fileName;
            System.err.println(error + " - " + e.getMessage());
            if (listener != null) listener.onPlaybackError(fileName, error);
            return new PlaybackResult(false, error);
            
        } catch (IOException e) {
            String error = "❌ Lỗi đọc file audio: " + fileName;
            System.err.println(error + " - " + e.getMessage());
            if (listener != null) listener.onPlaybackError(fileName, error);
            return new PlaybackResult(false, error);
            
        } catch (LineUnavailableException e) {
            String error = "❌ Không thể phát audio: " + fileName;
            System.err.println(error + " - " + e.getMessage());
            if (listener != null) listener.onPlaybackError(fileName, error);
            return new PlaybackResult(false, error);
            
        } catch (Exception e) {
            String error = "❌ Lỗi phát audio: " + e.getMessage();
            System.err.println("Error playing audio: " + e.getMessage());
            if (listener != null) listener.onPlaybackError(fileName, error);
            return new PlaybackResult(false, error);
        }
    }
    
    /**
     * Stop current audio playback
     */
    public void stopAudio() {
        try {
            if (currentClip != null && isPlaying) {
                isPlaying = false; // Set this first to prevent completion event
                currentClip.stop();
                currentClip.close();
                
                String fileName = currentAudioFile;
                currentAudioFile = null;
                
                if (listener != null) listener.onPlaybackStopped(fileName);
                System.out.println("🛑 Audio playback stopped: " + fileName);
            }
        } catch (Exception e) {
            System.err.println("❌ Error stopping audio: " + e.getMessage());
        }
    }
    
    /**
     * Pause/Resume audio playback
     */
    public void togglePause() {
        try {
            if (currentClip != null) {
                if (isPlaying) {
                    currentClip.stop();
                    isPlaying = false;
                    if (listener != null) listener.onPlaybackStopped(currentAudioFile);
                    System.out.println("⏸️ Audio paused: " + currentAudioFile);
                } else {
                    currentClip.start();
                    isPlaying = true;
                    if (listener != null) listener.onPlaybackStarted(currentAudioFile);
                    System.out.println("▶️ Audio resumed: " + currentAudioFile);
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error toggling pause: " + e.getMessage());
        }
    }
    
    /**
     * Get current playback position in seconds
     */
    public int getCurrentPosition() {
        try {
            if (currentClip != null) {
                long microseconds = currentClip.getMicrosecondPosition();
                return (int) (microseconds / 1_000_000);
            }
        } catch (Exception e) {
            System.err.println("❌ Error getting position: " + e.getMessage());
        }
        return 0;
    }
    
    /**
     * Get total duration in seconds
     */
    public int getTotalDuration() {
        try {
            if (currentClip != null) {
                long microseconds = currentClip.getMicrosecondLength();
                return (int) (microseconds / 1_000_000);
            }
        } catch (Exception e) {
            System.err.println("❌ Error getting duration: " + e.getMessage());
        }
        return 0;
    }
    
    /**
     * Check if audio is currently playing
     */
    public boolean isPlaying() {
        return isPlaying;
    }
    
    /**
     * Get currently playing file name
     */
    public String getCurrentAudioFile() {
        return currentAudioFile;
    }
    
    /**
     * Check if audio file format is supported
     */
    public static boolean isAudioSupported(String fileName) {
        if (fileName == null) return false;
        
        String extension = "";
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            extension = fileName.substring(lastDot + 1).toLowerCase();
        }
        
        // Java Sound API supports these formats by default
        return extension.equals("wav") || extension.equals("au") || extension.equals("aiff");
    }
    
    /**
     * Format time in MM:SS format
     */
    public static String formatTime(int seconds) {
        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;
        return String.format("%d:%02d", minutes, remainingSeconds);
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        stopAudio();
        if (currentClip != null) {
            currentClip.close();
            currentClip = null;
        }
    }
    
    /**
     * Playback result class
     */
    public static class PlaybackResult {
        private final boolean success;
        private final String message;
        
        public PlaybackResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }
}

package com.example.doancuoikyjava.service;

import javax.sound.sampled.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service for recording audio messages in chat
 */
public class AudioRecordingService {
    
    private static final String AUDIO_DIR = "uploads/chat/audio/";
    private static final AudioFormat AUDIO_FORMAT = new AudioFormat(
        AudioFormat.Encoding.PCM_SIGNED,
        44100,  // Sample rate
        16,     // Sample size in bits
        2,      // Channels (stereo)
        4,      // Frame size
        44100,  // Frame rate
        false   // Big endian
    );
    
    private TargetDataLine targetDataLine;
    private AudioInputStream audioInputStream;
    private boolean isRecording = false;
    private File currentRecordingFile;
    private long recordingStartTime;
    
    public AudioRecordingService() {
        initializeAudioDirectory();
    }
    
    /**
     * Initialize audio directory
     */
    private void initializeAudioDirectory() {
        try {
            Files.createDirectories(Paths.get(AUDIO_DIR));
            System.out.println("🎤 Audio directory initialized successfully");
        } catch (IOException e) {
            System.err.println("❌ Error creating audio directory: " + e.getMessage());
        }
    }
    
    /**
     * Start recording audio
     */
    public RecordingResult startRecording(String senderId) {
        try {
            // Check if microphone is available
            DataLine.Info info = new DataLine.Info(TargetDataLine.class, AUDIO_FORMAT);
            
            if (!AudioSystem.isLineSupported(info)) {
                return new RecordingResult(false, "❌ Microphone không được hỗ trợ", null);
            }
            
            // Get and open the target data line for recording
            targetDataLine = (TargetDataLine) AudioSystem.getLine(info);
            targetDataLine.open(AUDIO_FORMAT);
            targetDataLine.start();
            
            // Create audio input stream
            audioInputStream = new AudioInputStream(targetDataLine);
            
            // Generate unique filename
            String fileName = generateAudioFileName(senderId);
            currentRecordingFile = new File(AUDIO_DIR + fileName);
            
            // Start recording in a separate thread
            isRecording = true;
            recordingStartTime = System.currentTimeMillis();
            
            Thread recordingThread = new Thread(() -> {
                try {
                    AudioSystem.write(audioInputStream, AudioFileFormat.Type.WAVE, currentRecordingFile);
                } catch (IOException e) {
                    System.err.println("❌ Error during recording: " + e.getMessage());
                }
            });
            
            recordingThread.start();
            
            System.out.println("🎤 Recording started: " + fileName);
            return new RecordingResult(true, "🎤 Đang ghi âm...", fileName);
            
        } catch (LineUnavailableException e) {
            System.err.println("❌ Audio line unavailable: " + e.getMessage());
            return new RecordingResult(false, "❌ Không thể truy cập microphone", null);
        }
    }
    
    /**
     * Stop recording audio
     */
    public RecordingResult stopRecording() {
        if (!isRecording || targetDataLine == null) {
            return new RecordingResult(false, "❌ Không có recording nào đang diễn ra", null);
        }
        
        try {
            // Stop recording
            isRecording = false;
            targetDataLine.stop();
            targetDataLine.close();
            
            // Calculate duration
            long recordingEndTime = System.currentTimeMillis();
            int durationSeconds = (int) ((recordingEndTime - recordingStartTime) / 1000);
            
            // Check if recording is too short
            if (durationSeconds < 1) {
                // Delete the file if recording is too short
                if (currentRecordingFile != null && currentRecordingFile.exists()) {
                    currentRecordingFile.delete();
                }
                return new RecordingResult(false, "❌ Recording quá ngắn (tối thiểu 1 giây)", null);
            }
            
            System.out.println("🎤 Recording stopped. Duration: " + durationSeconds + " seconds");
            
            AudioInfo audioInfo = new AudioInfo(
                currentRecordingFile.getName(),
                currentRecordingFile.getAbsolutePath(),
                "audio/wav",
                currentRecordingFile.length(),
                durationSeconds
            );
            
            return new RecordingResult(true, "✅ Ghi âm hoàn thành", audioInfo);
            
        } catch (Exception e) {
            System.err.println("❌ Error stopping recording: " + e.getMessage());
            return new RecordingResult(false, "❌ Lỗi khi dừng ghi âm", null);
        }
    }
    
    /**
     * Cancel current recording
     */
    public void cancelRecording() {
        if (isRecording && targetDataLine != null) {
            isRecording = false;
            targetDataLine.stop();
            targetDataLine.close();
            
            // Delete the recording file
            if (currentRecordingFile != null && currentRecordingFile.exists()) {
                currentRecordingFile.delete();
                System.out.println("🗑️ Recording cancelled and file deleted");
            }
        }
    }
    
    /**
     * Check if currently recording
     */
    public boolean isRecording() {
        return isRecording;
    }
    
    /**
     * Get current recording duration in seconds
     */
    public int getCurrentRecordingDuration() {
        if (!isRecording) return 0;
        return (int) ((System.currentTimeMillis() - recordingStartTime) / 1000);
    }
    
    /**
     * Generate unique audio filename
     */
    private String generateAudioFileName(String senderId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("audio_%s_%s.wav", senderId, timestamp);
    }
    
    /**
     * Check if microphone is available
     */
    public static boolean isMicrophoneAvailable() {
        try {
            DataLine.Info info = new DataLine.Info(TargetDataLine.class, AUDIO_FORMAT);
            return AudioSystem.isLineSupported(info);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Recording result class
     */
    public static class RecordingResult {
        private final boolean success;
        private final String message;
        private final Object data; // Can be String (filename) or AudioInfo
        
        public RecordingResult(boolean success, String message, Object data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Object getData() { return data; }
        
        public String getFileName() {
            if (data instanceof String) return (String) data;
            if (data instanceof AudioInfo) return ((AudioInfo) data).getFileName();
            return null;
        }
        
        public AudioInfo getAudioInfo() {
            return data instanceof AudioInfo ? (AudioInfo) data : null;
        }
    }
    
    /**
     * Audio information class
     */
    public static class AudioInfo {
        private final String fileName;
        private final String filePath;
        private final String mimeType;
        private final long fileSize;
        private final int duration;
        
        public AudioInfo(String fileName, String filePath, String mimeType, long fileSize, int duration) {
            this.fileName = fileName;
            this.filePath = filePath;
            this.mimeType = mimeType;
            this.fileSize = fileSize;
            this.duration = duration;
        }
        
        public String getFileName() { return fileName; }
        public String getFilePath() { return filePath; }
        public String getMimeType() { return mimeType; }
        public long getFileSize() { return fileSize; }
        public int getDuration() { return duration; }
        
        public String getFormattedDuration() {
            int minutes = duration / 60;
            int seconds = duration % 60;
            return String.format("%d:%02d", minutes, seconds);
        }
        
        public String getFormattedFileSize() {
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            }
        }
    }
}

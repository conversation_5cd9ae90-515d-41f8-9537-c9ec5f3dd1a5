package com.example.doancuoikyjava.service;

import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * Service for handling file uploads in chat
 */
public class FileUploadService {
    
    private static final String UPLOAD_DIR = "uploads/";
    private static final String CHAT_FILES_DIR = UPLOAD_DIR + "chat/";
    private static final String IMAGES_DIR = CHAT_FILES_DIR + "images/";
    private static final String AUDIO_DIR = CHAT_FILES_DIR + "audio/";
    private static final String DOCUMENTS_DIR = CHAT_FILES_DIR + "documents/";
    
    // Supported file types
    private static final List<String> IMAGE_EXTENSIONS = Arrays.asList(
        "jpg", "jpeg", "png", "gif", "bmp", "webp"
    );
    
    private static final List<String> AUDIO_EXTENSIONS = Arrays.asList(
        "mp3", "wav", "m4a", "aac", "ogg", "flac"
    );
    
    private static final List<String> DOCUMENT_EXTENSIONS = Arrays.asList(
        "pdf", "doc", "docx", "txt", "rtf", "xls", "xlsx", "ppt", "pptx"
    );
    
    // Maximum file sizes (in bytes)
    private static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final long MAX_AUDIO_SIZE = 50 * 1024 * 1024; // 50MB
    private static final long MAX_DOCUMENT_SIZE = 20 * 1024 * 1024; // 20MB
    
    public FileUploadService() {
        initializeDirectories();
    }
    
    /**
     * Initialize upload directories
     */
    private void initializeDirectories() {
        try {
            Files.createDirectories(Paths.get(IMAGES_DIR));
            Files.createDirectories(Paths.get(AUDIO_DIR));
            Files.createDirectories(Paths.get(DOCUMENTS_DIR));
            System.out.println("📁 Upload directories initialized successfully");
        } catch (IOException e) {
            System.err.println("❌ Error creating upload directories: " + e.getMessage());
        }
    }
    
    /**
     * Show file chooser for images
     */
    public File chooseImageFile(Stage parentStage) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("🖼️ Chọn ảnh để gửi");
        
        // Set extension filters
        FileChooser.ExtensionFilter imageFilter = new FileChooser.ExtensionFilter(
            "🖼️ Ảnh (*.jpg, *.png, *.gif)", "*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.webp"
        );
        fileChooser.getExtensionFilters().add(imageFilter);
        
        return fileChooser.showOpenDialog(parentStage);
    }
    
    /**
     * Show file chooser for documents
     */
    public File chooseDocumentFile(Stage parentStage) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("📄 Chọn file để gửi");
        
        // Set extension filters
        FileChooser.ExtensionFilter documentFilter = new FileChooser.ExtensionFilter(
            "📄 Tài liệu", "*.pdf", "*.doc", "*.docx", "*.txt", "*.rtf", "*.xls", "*.xlsx", "*.ppt", "*.pptx"
        );
        FileChooser.ExtensionFilter allFilter = new FileChooser.ExtensionFilter(
            "📁 Tất cả file", "*.*"
        );
        fileChooser.getExtensionFilters().addAll(documentFilter, allFilter);
        
        return fileChooser.showOpenDialog(parentStage);
    }
    
    /**
     * Upload and save file
     */
    public FileUploadResult uploadFile(File sourceFile, String senderId) {
        if (sourceFile == null || !sourceFile.exists()) {
            return new FileUploadResult(false, "❌ File không tồn tại", null);
        }
        
        try {
            String fileName = sourceFile.getName();
            String extension = getFileExtension(fileName).toLowerCase();
            String fileType = getFileType(extension);
            long fileSize = sourceFile.length();
            
            // Validate file size
            if (!isFileSizeValid(fileType, fileSize)) {
                return new FileUploadResult(false, "❌ File quá lớn", null);
            }
            
            // Generate unique filename
            String uniqueFileName = generateUniqueFileName(fileName, senderId);
            String targetDir = getTargetDirectory(fileType);
            Path targetPath = Paths.get(targetDir + uniqueFileName);
            
            // Copy file
            Files.copy(sourceFile.toPath(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            System.out.println("✅ File uploaded successfully: " + uniqueFileName);
            
            return new FileUploadResult(true, "✅ Upload thành công", 
                new FileInfo(fileName, targetPath.toString(), getMimeType(extension), fileSize, fileType));
                
        } catch (IOException e) {
            System.err.println("❌ Error uploading file: " + e.getMessage());
            return new FileUploadResult(false, "❌ Lỗi upload file: " + e.getMessage(), null);
        }
    }
    
    /**
     * Get file extension
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot + 1) : "";
    }
    
    /**
     * Determine file type based on extension
     */
    private String getFileType(String extension) {
        if (IMAGE_EXTENSIONS.contains(extension)) {
            return "IMAGE";
        } else if (AUDIO_EXTENSIONS.contains(extension)) {
            return "AUDIO";
        } else {
            return "DOCUMENT";
        }
    }
    
    /**
     * Get target directory based on file type
     */
    private String getTargetDirectory(String fileType) {
        switch (fileType) {
            case "IMAGE": return IMAGES_DIR;
            case "AUDIO": return AUDIO_DIR;
            default: return DOCUMENTS_DIR;
        }
    }
    
    /**
     * Validate file size
     */
    private boolean isFileSizeValid(String fileType, long fileSize) {
        switch (fileType) {
            case "IMAGE": return fileSize <= MAX_IMAGE_SIZE;
            case "AUDIO": return fileSize <= MAX_AUDIO_SIZE;
            case "DOCUMENT": return fileSize <= MAX_DOCUMENT_SIZE;
            default: return fileSize <= MAX_DOCUMENT_SIZE;
        }
    }
    
    /**
     * Generate unique filename
     */
    private String generateUniqueFileName(String originalName, String senderId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String extension = getFileExtension(originalName);
        String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
        
        return String.format("%s_%s_%s.%s", senderId, timestamp, baseName, extension);
    }
    
    /**
     * Get MIME type
     */
    private String getMimeType(String extension) {
        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            case "gif": return "image/gif";
            case "pdf": return "application/pdf";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt": return "text/plain";
            case "mp3": return "audio/mpeg";
            case "wav": return "audio/wav";
            default: return "application/octet-stream";
        }
    }
    
    /**
     * File upload result class
     */
    public static class FileUploadResult {
        private final boolean success;
        private final String message;
        private final FileInfo fileInfo;
        
        public FileUploadResult(boolean success, String message, FileInfo fileInfo) {
            this.success = success;
            this.message = message;
            this.fileInfo = fileInfo;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public FileInfo getFileInfo() { return fileInfo; }
    }
    
    /**
     * File information class
     */
    public static class FileInfo {
        private final String originalName;
        private final String filePath;
        private final String mimeType;
        private final long fileSize;
        private final String fileType;
        
        public FileInfo(String originalName, String filePath, String mimeType, long fileSize, String fileType) {
            this.originalName = originalName;
            this.filePath = filePath;
            this.mimeType = mimeType;
            this.fileSize = fileSize;
            this.fileType = fileType;
        }
        
        public String getOriginalName() { return originalName; }
        public String getFilePath() { return filePath; }
        public String getMimeType() { return mimeType; }
        public long getFileSize() { return fileSize; }
        public String getFileType() { return fileType; }
    }
}

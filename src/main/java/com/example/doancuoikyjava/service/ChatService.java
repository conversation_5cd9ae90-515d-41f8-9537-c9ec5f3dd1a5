package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service class for handling chat-related database operations
 */
public class ChatService {
    
    private final DatabaseConnection dbConnection;
    private final UserService userService;
    private final CourseService courseService;
    
    public ChatService() {
        this.dbConnection = new DatabaseConnection();
        this.userService = new UserService();
        this.courseService = new CourseService();
        initializeChatTables();
    }
    
    /**
     * Initialize chat-related database tables
     */
    private void initializeChatTables() {
        try (Connection conn = dbConnection.getConnection()) {
            // Create chat_messages table
            String createChatMessagesTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='chat_messages' AND xtype='U')
                CREATE TABLE chat_messages (
                    message_id NVARCHAR(100) PRIMARY KEY,
                    sender_id NVARCHAR(50) NOT NULL,
                    sender_name NVARCHAR(100) NOT NULL,
                    receiver_id NVARCHAR(50) NOT NULL,
                    receiver_name NVARCHAR(100) NOT NULL,
                    content NTEXT NOT NULL,
                    message_type NVARCHAR(20) DEFAULT 'TEXT',
                    timestamp DATETIME2 DEFAULT GETDATE(),
                    is_read BIT DEFAULT 0,
                    course_id NVARCHAR(50),
                    course_name NVARCHAR(200),
                    FOREIGN KEY (sender_id) REFERENCES users(user_id),
                    FOREIGN KEY (receiver_id) REFERENCES users(user_id)
                )
            """;
            
            try (PreparedStatement stmt = conn.prepareStatement(createChatMessagesTable)) {
                stmt.executeUpdate();
                System.out.println("✅ Chat messages table initialized");
            }
            
            // Create chat_participants table for course-based chats
            String createChatParticipantsTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='chat_participants' AND xtype='U')
                CREATE TABLE chat_participants (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    course_id NVARCHAR(50) NOT NULL,
                    user_id NVARCHAR(50) NOT NULL,
                    user_role NVARCHAR(20) NOT NULL,
                    joined_at DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (user_id) REFERENCES users(user_id),
                    UNIQUE(course_id, user_id)
                )
            """;
            
            try (PreparedStatement stmt = conn.prepareStatement(createChatParticipantsTable)) {
                stmt.executeUpdate();
                System.out.println("✅ Chat participants table initialized");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error initializing chat tables: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Save a chat message to database
     */
    public boolean saveMessage(ChatMessage message) {
        String sql = """
            INSERT INTO chat_messages 
            (message_id, sender_id, sender_name, receiver_id, receiver_name, 
             content, message_type, timestamp, is_read, course_id, course_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, message.getMessageId());
            stmt.setString(2, message.getSenderId());
            stmt.setString(3, message.getSenderName());
            stmt.setString(4, message.getReceiverId());
            stmt.setString(5, message.getReceiverName());
            stmt.setString(6, message.getContent());
            stmt.setString(7, message.getType().name());
            stmt.setTimestamp(8, Timestamp.valueOf(message.getTimestamp()));
            stmt.setBoolean(9, message.isRead());
            stmt.setString(10, message.getCourseId());
            stmt.setString(11, message.getCourseName());
            
            int result = stmt.executeUpdate();
            System.out.println("💾 Message saved: " + message.getMessageId());
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("❌ Error saving message: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get chat history between two users
     */
    public List<ChatMessage> getChatHistory(String userId1, String userId2) {
        String sql = """
            SELECT * FROM chat_messages 
            WHERE (sender_id = ? AND receiver_id = ?) 
               OR (sender_id = ? AND receiver_id = ?)
            ORDER BY timestamp ASC
        """;
        
        List<ChatMessage> messages = new ArrayList<>();
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, userId1);
            stmt.setString(2, userId2);
            stmt.setString(3, userId2);
            stmt.setString(4, userId1);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ChatMessage message = mapResultSetToMessage(rs);
                    messages.add(message);
                }
            }
            
            System.out.println("📜 Retrieved " + messages.size() + " messages between " + userId1 + " and " + userId2);
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting chat history: " + e.getMessage());
            e.printStackTrace();
        }
        
        return messages;
    }
    
    /**
     * Get recent conversations for a user
     */
    public List<ChatMessage> getRecentConversations(String userId) {
        String sql = """
            WITH RankedMessages AS (
                SELECT *,
                       ROW_NUMBER() OVER (
                           PARTITION BY 
                               CASE 
                                   WHEN sender_id = ? THEN receiver_id 
                                   ELSE sender_id 
                               END
                           ORDER BY timestamp DESC
                       ) as rn
                FROM chat_messages 
                WHERE sender_id = ? OR receiver_id = ?
            )
            SELECT * FROM RankedMessages 
            WHERE rn = 1 
            ORDER BY timestamp DESC
        """;
        
        List<ChatMessage> conversations = new ArrayList<>();
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, userId);
            stmt.setString(2, userId);
            stmt.setString(3, userId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ChatMessage message = mapResultSetToMessage(rs);
                    conversations.add(message);
                }
            }
            
            System.out.println("💬 Retrieved " + conversations.size() + " recent conversations for " + userId);
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting recent conversations: " + e.getMessage());
            e.printStackTrace();
        }
        
        return conversations;
    }
    
    /**
     * Mark a message as read
     */
    public boolean markMessageAsRead(String messageId) {
        String sql = "UPDATE chat_messages SET is_read = 1 WHERE message_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, messageId);
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ Message marked as read: " + messageId);
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error marking message as read: " + e.getMessage());
            e.printStackTrace();
        }
        
        return false;
    }
    
    /**
     * Get unread message count for a user
     */
    public int getUnreadMessageCount(String userId) {
        String sql = "SELECT COUNT(*) FROM chat_messages WHERE receiver_id = ? AND is_read = 0";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, userId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting unread message count: " + e.getMessage());
            e.printStackTrace();
        }
        
        return 0;
    }
    
    /**
     * Get available teachers for a student to chat with (based on enrolled courses)
     */
    public List<Teacher> getAvailableTeachersForStudent(String studentId) {
        List<Teacher> teachers = new ArrayList<>();

        try {
            // Get all teachers for now - simplified implementation
            List<User> allUsers = userService.getAllUsers();

            for (User user : allUsers) {
                if (user instanceof Teacher) {
                    teachers.add((Teacher) user);
                }
            }

            System.out.println("👨‍🏫 Found " + teachers.size() + " available teachers for student " + studentId);

        } catch (Exception e) {
            System.err.println("❌ Error getting available teachers: " + e.getMessage());
            e.printStackTrace();
        }

        return teachers;
    }
    
    /**
     * Get available students for a teacher to chat with (based on teaching courses)
     */
    public List<Student> getAvailableStudentsForTeacher(String teacherId) {
        List<Student> students = new ArrayList<>();

        try {
            // Get all students for now - simplified implementation
            List<User> allUsers = userService.getAllUsers();

            for (User user : allUsers) {
                if (user instanceof Student) {
                    students.add((Student) user);
                }
            }

            System.out.println("👨‍🎓 Found " + students.size() + " available students for teacher " + teacherId);

        } catch (Exception e) {
            System.err.println("❌ Error getting available students: " + e.getMessage());
            e.printStackTrace();
        }

        return students;
    }

    /**
     * Get available teachers for a teacher to chat with (based on teaching same courses)
     */
    public List<Teacher> getAvailableTeachersForTeacher(String teacherId) {
        List<Teacher> availableTeachers = new ArrayList<>();

        try {
            // Get current teacher's courses
            CourseService courseService = new CourseService();
            List<Course> teacherCourses = courseService.getCoursesByTeacher(teacherId);

            if (teacherCourses == null || teacherCourses.isEmpty()) {
                System.out.println("⚠️ Teacher " + teacherId + " has no courses assigned");
                return availableTeachers;
            }

            // Get course IDs that this teacher teaches
            Set<String> teacherCourseIds = teacherCourses.stream()
                    .filter(course -> course != null && course.getCourseId() != null)
                    .map(Course::getCourseId)
                    .collect(Collectors.toSet());

            System.out.println("📚 Teacher " + teacherId + " teaches courses: " + teacherCourseIds);

            // Get all teachers
            List<User> allUsers = userService.getAllUsers();

            for (User user : allUsers) {
                if (user instanceof Teacher && !user.getUserId().equals(teacherId)) {
                    Teacher otherTeacher = (Teacher) user;

                    // Get courses taught by this other teacher
                    List<Course> otherTeacherCourses = courseService.getCoursesByTeacher(otherTeacher.getTeacherId());

                    if (otherTeacherCourses != null && !otherTeacherCourses.isEmpty()) {
                        // Check if they teach any common courses
                        boolean hasCommonCourse = otherTeacherCourses.stream()
                                .filter(course -> course != null && course.getCourseId() != null)
                                .anyMatch(course -> teacherCourseIds.contains(course.getCourseId()));

                        if (hasCommonCourse) {
                            availableTeachers.add(otherTeacher);
                            System.out.println("✅ Teacher " + otherTeacher.getTeacherId() + " (" + otherTeacher.getFullName() + ") teaches common courses");
                        } else {
                            System.out.println("❌ Teacher " + otherTeacher.getTeacherId() + " (" + otherTeacher.getFullName() + ") has no common courses");
                        }
                    }
                }
            }

            System.out.println("👨‍🏫 Found " + availableTeachers.size() + " teachers with common courses for teacher " + teacherId);

        } catch (Exception e) {
            System.err.println("❌ Error getting available teachers for teacher: " + e.getMessage());
            e.printStackTrace();
        }

        return availableTeachers;
    }
    
    /**
     * Map ResultSet to ChatMessage object
     */
    private ChatMessage mapResultSetToMessage(ResultSet rs) throws SQLException {
        ChatMessage message = new ChatMessage();
        
        message.setMessageId(rs.getString("message_id"));
        message.setSenderId(rs.getString("sender_id"));
        message.setSenderName(rs.getString("sender_name"));
        message.setReceiverId(rs.getString("receiver_id"));
        message.setReceiverName(rs.getString("receiver_name"));
        message.setContent(rs.getString("content"));
        message.setType(ChatMessage.MessageType.valueOf(rs.getString("message_type")));
        message.setTimestamp(rs.getTimestamp("timestamp").toLocalDateTime());
        message.setRead(rs.getBoolean("is_read"));
        message.setCourseId(rs.getString("course_id"));
        message.setCourseName(rs.getString("course_name"));
        
        return message;
    }
}

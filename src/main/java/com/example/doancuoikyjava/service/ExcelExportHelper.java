package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class ExcelExportHelper {
    
    private static final String EXPORT_DIRECTORY = "exports";
    
    /**
     * Export danh sách điểm ra Excel
     */
    public String exportGrades(List<Grade> grades) {
        try {
            System.out.println("📊 Starting export of " + grades.size() + " grades to Excel...");
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Bảng điểm");
            
            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "STT", "Mã SV", "Tên sinh viên", "Mã môn", "Tên môn", 
                "Điểm số", "Điểm chữ", "Tín chỉ", "Học kỳ", "Mã GV", "Ngày nhập"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Grade grade : grades) {
                if (grade == null) continue;
                
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                
                // STT
                createCell(row, colNum++, rowNum - 1, dataStyle);
                // Mã SV
                createCell(row, colNum++, grade.getStudentId(), dataStyle);
                // Tên sinh viên
                createCell(row, colNum++, grade.getStudentName(), dataStyle);
                // Mã môn
                createCell(row, colNum++, grade.getCourseId(), dataStyle);
                // Tên môn
                createCell(row, colNum++, grade.getCourseName(), dataStyle);
                // Điểm số
                createCell(row, colNum++, grade.getScore(), dataStyle);
                // Điểm chữ
                createCell(row, colNum++, grade.getLetterGrade(), dataStyle);
                // Tín chỉ
                createCell(row, colNum++, grade.getCredits(), dataStyle);
                // Học kỳ
                createCell(row, colNum++, grade.getSemester(), dataStyle);
                // Mã GV
                createCell(row, colNum++, grade.getTeacherId(), dataStyle);
                // Ngày nhập
                createCell(row, colNum++, grade.getCreatedAt() != null ? 
                    grade.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "", dataStyle);
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Save file
            String fileName = generateFileName("BangDiem");
            String filePath = saveWorkbook(workbook, fileName);
            
            workbook.close();
            
            System.out.println("✅ Successfully exported " + grades.size() + " grades to: " + filePath);
            return filePath;
            
        } catch (Exception e) {
            System.err.println("❌ Error exporting grades to Excel: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Create header style
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // Background color
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // Font
        Font font = workbook.createFont();
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        // Borders
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // Alignment
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * Create data style
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // Borders
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // Alignment
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * Create cell with value and style
     */
    private void createCell(Row row, int column, Object value, CellStyle style) {
        Cell cell = row.createCell(column);
        
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
        
        cell.setCellStyle(style);
    }
    
    /**
     * Generate file name with timestamp
     */
    private String generateFileName(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + ".xlsx";
    }
    
    /**
     * Save workbook to file
     */
    private String saveWorkbook(Workbook workbook, String fileName) throws IOException {
        // Create export directory if not exists
        File exportDir = new File(EXPORT_DIRECTORY);
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        
        // Create file path
        String filePath = EXPORT_DIRECTORY + File.separator + fileName;
        
        // Save workbook
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        }
        
        return filePath;
    }
    
    /**
     * Get export directory path
     */
    public String getExportDirectory() {
        return EXPORT_DIRECTORY;
    }
    
    /**
     * Export student's personal grades
     */
    public String exportStudentGrades(String studentId, List<Grade> grades) {
        try {
            System.out.println("📊 Starting export of student grades for: " + studentId);
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Bảng điểm cá nhân");
            
            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "STT", "Mã môn", "Tên môn", "Điểm số", "Điểm chữ", 
                "Tín chỉ", "Học kỳ", "Ngày nhập"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Grade grade : grades) {
                if (grade == null || !studentId.equals(grade.getStudentId())) continue;
                
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                
                // STT
                createCell(row, colNum++, rowNum - 1, dataStyle);
                // Mã môn
                createCell(row, colNum++, grade.getCourseId(), dataStyle);
                // Tên môn
                createCell(row, colNum++, grade.getCourseName(), dataStyle);
                // Điểm số
                createCell(row, colNum++, grade.getScore(), dataStyle);
                // Điểm chữ
                createCell(row, colNum++, grade.getLetterGrade(), dataStyle);
                // Tín chỉ
                createCell(row, colNum++, grade.getCredits(), dataStyle);
                // Học kỳ
                createCell(row, colNum++, grade.getSemester(), dataStyle);
                // Ngày nhập
                createCell(row, colNum++, grade.getCreatedAt() != null ? 
                    grade.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "", dataStyle);
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Save file
            String fileName = generateFileName("BangDiem_" + studentId);
            String filePath = saveWorkbook(workbook, fileName);
            
            workbook.close();
            
            System.out.println("✅ Successfully exported student grades to: " + filePath);
            return filePath;
            
        } catch (Exception e) {
            System.err.println("❌ Error exporting student grades to Excel: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Export teacher's course grades
     */
    public String exportTeacherGrades(String teacherId, List<Grade> grades) {
        try {
            System.out.println("📊 Starting export of teacher grades for: " + teacherId);
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Bảng điểm môn học");
            
            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "STT", "Mã SV", "Tên sinh viên", "Mã môn", "Tên môn", 
                "Điểm số", "Điểm chữ", "Tín chỉ", "Học kỳ", "Ngày nhập"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Grade grade : grades) {
                if (grade == null || !teacherId.equals(grade.getTeacherId())) continue;
                
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                
                // STT
                createCell(row, colNum++, rowNum - 1, dataStyle);
                // Mã SV
                createCell(row, colNum++, grade.getStudentId(), dataStyle);
                // Tên sinh viên
                createCell(row, colNum++, grade.getStudentName(), dataStyle);
                // Mã môn
                createCell(row, colNum++, grade.getCourseId(), dataStyle);
                // Tên môn
                createCell(row, colNum++, grade.getCourseName(), dataStyle);
                // Điểm số
                createCell(row, colNum++, grade.getScore(), dataStyle);
                // Điểm chữ
                createCell(row, colNum++, grade.getLetterGrade(), dataStyle);
                // Tín chỉ
                createCell(row, colNum++, grade.getCredits(), dataStyle);
                // Học kỳ
                createCell(row, colNum++, grade.getSemester(), dataStyle);
                // Ngày nhập
                createCell(row, colNum++, grade.getCreatedAt() != null ? 
                    grade.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "", dataStyle);
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Save file
            String fileName = generateFileName("BangDiem_GV_" + teacherId);
            String filePath = saveWorkbook(workbook, fileName);
            
            workbook.close();
            
            System.out.println("✅ Successfully exported teacher grades to: " + filePath);
            return filePath;
            
        } catch (Exception e) {
            System.err.println("❌ Error exporting teacher grades to Excel: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}

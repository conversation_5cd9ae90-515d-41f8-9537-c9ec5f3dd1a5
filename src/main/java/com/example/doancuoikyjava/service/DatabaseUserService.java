package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class DatabaseUserService {
    
    /**
     * Xác thực đăng nhập
     */
    public User authenticate(String username, String password, User.UserRole role) {
        String sql = """
            SELECT u.*, s.student_id, s.class_name, s.major, s.year_of_study, s.gpa,
                   t.teacher_id, t.department, t.position, t.salary, t.qualification, t.experience_years
            FROM Users u
            LEFT JOIN Students s ON u.user_id = s.user_id
            LEFT JOIN Teachers t ON u.user_id = t.user_id
            WHERE u.username = ? AND u.password = ? AND u.role = ?
            """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, username);
            stmt.setString(2, password);
            stmt.setString(3, role.toString());
            
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return createUserFromResultSet(rs, role);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi xác thực: " + e.getMessage());
        }
        
        return null;
    }

    /**
     * Lấy tất cả users
     */
    public List<User> getAllUsers() {
        List<User> allUsers = new ArrayList<>();

        try {
            // Get all users from all roles
            for (User.UserRole role : User.UserRole.values()) {
                List<User> usersOfRole = getUsersByRole(role);
                if (usersOfRole != null) {
                    allUsers.addAll(usersOfRole);
                }
            }

            System.out.println("📊 Loaded " + allUsers.size() + " total users from database");

        } catch (Exception e) {
            System.err.println("❌ Error getting all users: " + e.getMessage());
            e.printStackTrace();
        }

        return allUsers;
    }

    /**
     * Lấy tất cả users theo role
     */
    public List<User> getUsersByRole(User.UserRole role) {
        List<User> users = new ArrayList<>();
        String sql = """
            SELECT u.*, s.student_id, s.class_name, s.major, s.year_of_study, s.gpa,
                   t.teacher_id, t.department, t.position, t.salary, t.qualification, t.experience_years
            FROM Users u
            LEFT JOIN Students s ON u.user_id = s.user_id
            LEFT JOIN Teachers t ON u.user_id = t.user_id
            WHERE u.role = ?
            """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, role.toString());
            
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                User user = createUserFromResultSet(rs, role);
                if (user != null) {
                    users.add(user);
                }
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy danh sách users: " + e.getMessage());
        }
        
        return users;
    }
    
    /**
     * Lấy user theo ID
     */
    public User getUserById(String userId) {
        String sql = """
            SELECT u.*, s.student_id, s.class_name, s.major, s.year_of_study, s.gpa,
                   t.teacher_id, t.department, t.position, t.salary, t.qualification, t.experience_years
            FROM Users u
            LEFT JOIN Students s ON u.user_id = s.user_id
            LEFT JOIN Teachers t ON u.user_id = t.user_id
            WHERE u.user_id = ?
            """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, userId);
            
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                User.UserRole role = User.UserRole.valueOf(rs.getString("role"));
                return createUserFromResultSet(rs, role);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy user theo ID: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Kiểm tra username đã tồn tại
     */
    public boolean isUsernameExists(String username) {
        String sql = "SELECT COUNT(*) FROM Users WHERE username = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, username);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }

        } catch (SQLException e) {
            System.err.println("❌ Lỗi kiểm tra username: " + e.getMessage());
        }

        return false;
    }

    /**
     * Kiểm tra user ID đã tồn tại
     */
    public boolean isUserIdExists(String userId) {
        String sql = "SELECT COUNT(*) FROM Users WHERE user_id = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, userId);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }

        } catch (SQLException e) {
            System.err.println("❌ Lỗi kiểm tra user ID: " + e.getMessage());
        }

        return false;
    }

    /**
     * Thêm user mới
     */
    public boolean addUser(User user) {
        System.out.println("🔍 Đang thêm user: " + user.getUsername() + " (ID: " + user.getUserId() + ")");

        // Kiểm tra username đã tồn tại
        if (isUsernameExists(user.getUsername())) {
            System.out.println("❌ Username đã tồn tại: " + user.getUsername());
            return false;
        }

        // Kiểm tra user ID đã tồn tại
        if (isUserIdExists(user.getUserId())) {
            System.out.println("❌ User ID đã tồn tại: " + user.getUserId());
            return false;
        }

        System.out.println("✅ Validation passed, đang insert vào database...");

        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false); // Bắt đầu transaction
            
            // Thêm vào bảng Users
            String userSql = """
                INSERT INTO Users (user_id, username, password, full_name, email, phone, 
                                 date_of_birth, address, role)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;
            
            PreparedStatement userStmt = conn.prepareStatement(userSql);
            userStmt.setString(1, user.getUserId());
            userStmt.setString(2, user.getUsername());
            userStmt.setString(3, user.getPassword());
            userStmt.setString(4, user.getFullName());
            userStmt.setString(5, user.getEmail());
            userStmt.setString(6, user.getPhone());
            userStmt.setDate(7, user.getDateOfBirth() != null ? Date.valueOf(user.getDateOfBirth()) : null);
            userStmt.setString(8, user.getAddress());
            userStmt.setString(9, user.getRole().toString());
            
            userStmt.executeUpdate();
            
            // Thêm vào bảng con tương ứng
            if (user instanceof Student) {
                addStudentDetails(conn, (Student) user);
            } else if (user instanceof Teacher) {
                addTeacherDetails(conn, (Teacher) user);
            }

            conn.commit(); // Commit transaction
            System.out.println("✅ Thêm user thành công: " + user.getUsername() + " (ID: " + user.getUserId() + ")");
            return true;
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi thêm user: " + e.getMessage());
            if (conn != null) {
                try {
                    conn.rollback(); // Rollback nếu có lỗi
                } catch (SQLException ex) {
                    System.err.println("❌ Lỗi rollback: " + ex.getMessage());
                }
            }
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true); // Khôi phục auto commit
                } catch (SQLException e) {
                    System.err.println("❌ Lỗi khôi phục auto commit: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Cập nhật user
     */
    public boolean updateUser(User user) {
        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false);
            
            // Cập nhật bảng Users
            String userSql = """
                UPDATE Users SET username = ?, password = ?, full_name = ?, email = ?, 
                               phone = ?, date_of_birth = ?, address = ?
                WHERE user_id = ?
                """;
            
            PreparedStatement userStmt = conn.prepareStatement(userSql);
            userStmt.setString(1, user.getUsername());
            userStmt.setString(2, user.getPassword());
            userStmt.setString(3, user.getFullName());
            userStmt.setString(4, user.getEmail());
            userStmt.setString(5, user.getPhone());
            userStmt.setDate(6, user.getDateOfBirth() != null ? Date.valueOf(user.getDateOfBirth()) : null);
            userStmt.setString(7, user.getAddress());
            userStmt.setString(8, user.getUserId());
            
            userStmt.executeUpdate();
            
            // Cập nhật bảng con
            if (user instanceof Student) {
                updateStudentDetails(conn, (Student) user);
            } else if (user instanceof Teacher) {
                updateTeacherDetails(conn, (Teacher) user);
            }
            
            conn.commit();
            return true;
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi cập nhật user: " + e.getMessage());
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    System.err.println("❌ Lỗi rollback: " + ex.getMessage());
                }
            }
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                    System.err.println("❌ Lỗi khôi phục auto commit: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Xóa user với cascade delete - Enhanced version
     */
    public boolean deleteUser(String userId) {
        System.out.println("🔍 =================================");
        System.out.println("🔍 BẮT ĐẦU XÓA USER: " + userId);
        System.out.println("🔍 =================================");

        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            System.out.println("✅ Kết nối database thành công");

            // Disable auto-commit
            conn.setAutoCommit(false);
            System.out.println("✅ Đã tắt auto-commit");

            try {
                // Kiểm tra user có tồn tại không
                System.out.println("🔍 Kiểm tra user tồn tại...");
                if (!checkUserExists(conn, userId)) {
                    System.err.println("❌ User không tồn tại: " + userId);
                    conn.rollback();
                    return false;
                }

                // Kiểm tra user role
                String userRole = getUserRole(conn, userId);
                System.out.println("📋 User role: " + userRole);

                // Xóa dữ liệu liên quan trước
                System.out.println("🔄 BƯỚC 1: Xóa dữ liệu liên quan...");
                deleteRelatedData(conn, userId);

                // Xóa từ bảng Teachers hoặc Students
                System.out.println("🔄 BƯỚC 2: Xóa từ bảng role...");
                deleteFromRoleTable(conn, userId);

                // Cuối cùng xóa từ bảng Users
                System.out.println("🔄 BƯỚC 3: Xóa từ bảng Users...");
                String userSql = "DELETE FROM Users WHERE user_id = ?";
                PreparedStatement userStmt = conn.prepareStatement(userSql);
                userStmt.setString(1, userId);

                int result = userStmt.executeUpdate();
                System.out.println("📊 Số dòng bị ảnh hưởng trong Users: " + result);

                if (result > 0) {
                    conn.commit();
                    System.out.println("✅ =================================");
                    System.out.println("✅ XÓA USER THÀNH CÔNG: " + userId);
                    System.out.println("✅ =================================");
                    return true;
                } else {
                    conn.rollback();
                    System.err.println("❌ Không có dòng nào bị xóa trong bảng Users");
                    System.err.println("❌ Có thể user_id không đúng hoặc đã bị xóa");
                    return false;
                }

            } catch (SQLException e) {
                System.err.println("❌ Lỗi trong transaction, đang rollback...");
                System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
                try {
                    conn.rollback();
                    System.out.println("✅ Rollback thành công");
                } catch (SQLException rollbackEx) {
                    System.err.println("❌ Lỗi khi rollback: " + rollbackEx.getMessage());
                }
                throw e;
            }

        } catch (SQLException e) {
            System.err.println("❌ =================================");
            System.err.println("❌ LỖI XÓA USER: " + userId);
            System.err.println("❌ Message: " + e.getMessage());
            System.err.println("❌ SQL State: " + e.getSQLState());
            System.err.println("❌ Error Code: " + e.getErrorCode());
            System.err.println("❌ =================================");
            e.printStackTrace();
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    System.out.println("✅ Đã bật lại auto-commit");
                } catch (SQLException e) {
                    System.err.println("❌ Lỗi khi bật lại auto-commit: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Kiểm tra user có tồn tại không
     */
    private boolean checkUserExists(Connection conn, String userId) throws SQLException {
        String sql = "SELECT COUNT(*) FROM Users WHERE user_id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, userId);
        ResultSet rs = stmt.executeQuery();
        rs.next();
        int count = rs.getInt(1);
        System.out.println("📊 User tồn tại: " + (count > 0) + " (count: " + count + ")");
        return count > 0;
    }

    /**
     * Lấy role của user
     */
    private String getUserRole(Connection conn, String userId) throws SQLException {
        String sql = "SELECT role FROM Users WHERE user_id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, userId);
        ResultSet rs = stmt.executeQuery();
        if (rs.next()) {
            return rs.getString("role");
        }
        return "UNKNOWN";
    }

    /**
     * Xóa dữ liệu liên quan đến user với error handling chi tiết
     */
    private void deleteRelatedData(Connection conn, String userId) throws SQLException {
        int totalDeleted = 0;
        System.out.println("🔍 Bắt đầu xóa dữ liệu liên quan cho user: " + userId);

        // 1. Xóa từ Course_Enrollments
        try {
            String enrollmentSql = "DELETE FROM Course_Enrollments WHERE student_id = ?";
            PreparedStatement enrollmentStmt = conn.prepareStatement(enrollmentSql);
            enrollmentStmt.setString(1, userId);
            int enrollmentDeleted = enrollmentStmt.executeUpdate();
            totalDeleted += enrollmentDeleted;
            System.out.println("📊 Xóa Course_Enrollments: " + enrollmentDeleted + " dòng");
        } catch (SQLException e) {
            System.out.println("⚠️ Lỗi khi xóa Course_Enrollments: " + e.getMessage());
            // Không throw exception, tiếp tục với bảng khác
        }

        // 2. Xóa từ Grades
        try {
            String gradesSql = "DELETE FROM Grades WHERE student_id = ?";
            PreparedStatement gradesStmt = conn.prepareStatement(gradesSql);
            gradesStmt.setString(1, userId);
            int gradesDeleted = gradesStmt.executeUpdate();
            totalDeleted += gradesDeleted;
            System.out.println("📊 Xóa Grades: " + gradesDeleted + " dòng");
        } catch (SQLException e) {
            System.out.println("⚠️ Lỗi khi xóa Grades: " + e.getMessage());
        }

        // 3. Cập nhật Courses nếu user là teacher
        try {
            String updateCoursesSql = "UPDATE Courses SET teacher_id = NULL, teacher_name = NULL WHERE teacher_id = ?";
            PreparedStatement updateCoursesStmt = conn.prepareStatement(updateCoursesSql);
            updateCoursesStmt.setString(1, userId);
            int coursesUpdated = updateCoursesStmt.executeUpdate();
            System.out.println("📊 Cập nhật Courses: " + coursesUpdated + " dòng");
        } catch (SQLException e) {
            System.out.println("⚠️ Lỗi khi cập nhật Courses: " + e.getMessage());
        }

        // 4. Xóa từ Course_Schedule
        try {
            String scheduleSql = "DELETE FROM Course_Schedule WHERE teacher_id = ? OR student_id = ?";
            PreparedStatement scheduleStmt = conn.prepareStatement(scheduleSql);
            scheduleStmt.setString(1, userId);
            scheduleStmt.setString(2, userId);
            int scheduleDeleted = scheduleStmt.executeUpdate();
            totalDeleted += scheduleDeleted;
            System.out.println("📊 Xóa Course_Schedule: " + scheduleDeleted + " dòng");
        } catch (SQLException e) {
            System.out.println("⚠️ Lỗi khi xóa Course_Schedule: " + e.getMessage());
        }

        // 5. Xóa từ Notifications (nếu có)
        try {
            String notificationsSql = "DELETE FROM Notifications WHERE user_id = ?";
            PreparedStatement notificationsStmt = conn.prepareStatement(notificationsSql);
            notificationsStmt.setString(1, userId);
            int notificationsDeleted = notificationsStmt.executeUpdate();
            totalDeleted += notificationsDeleted;
            System.out.println("📊 Xóa Notifications: " + notificationsDeleted + " dòng");
        } catch (SQLException e) {
            System.out.println("⚠️ Bảng Notifications không tồn tại hoặc lỗi: " + e.getMessage());
        }

        System.out.println("✅ Hoàn thành xóa dữ liệu liên quan: " + totalDeleted + " dòng cho user: " + userId);
    }

    /**
     * Xóa từ bảng Teachers hoặc Students
     */
    private void deleteFromRoleTable(Connection conn, String userId) throws SQLException {
        int totalDeleted = 0;

        // Xóa từ Teachers
        String teacherSql = "DELETE FROM Teachers WHERE user_id = ?";
        PreparedStatement teacherStmt = conn.prepareStatement(teacherSql);
        teacherStmt.setString(1, userId);
        int teacherDeleted = teacherStmt.executeUpdate();
        totalDeleted += teacherDeleted;
        System.out.println("📊 Xóa Teachers: " + teacherDeleted + " dòng");

        // Xóa từ Students
        String studentSql = "DELETE FROM Students WHERE user_id = ?";
        PreparedStatement studentStmt = conn.prepareStatement(studentSql);
        studentStmt.setString(1, userId);
        int studentDeleted = studentStmt.executeUpdate();
        totalDeleted += studentDeleted;
        System.out.println("📊 Xóa Students: " + studentDeleted + " dòng");

        System.out.println("✅ Tổng cộng đã xóa từ bảng role: " + totalDeleted + " dòng cho user: " + userId);

        if (totalDeleted == 0) {
            System.out.println("⚠️ Cảnh báo: Không tìm thấy user trong bảng Teachers hoặc Students");
        }
    }
    
    /**
     * Tạo User object từ ResultSet
     */
    private User createUserFromResultSet(ResultSet rs, User.UserRole role) throws SQLException {
        User user = null;
        
        switch (role) {
            case STUDENT:
                Student student = new Student();
                student.setStudentId(rs.getString("student_id"));
                student.setClassName(rs.getString("class_name"));
                student.setMajor(rs.getString("major"));
                student.setYear(rs.getInt("year_of_study"));
                student.setGpa(rs.getDouble("gpa"));
                user = student;
                break;
                
            case TEACHER:
                Teacher teacher = new Teacher();
                teacher.setTeacherId(rs.getString("teacher_id"));
                teacher.setDepartment(rs.getString("department"));
                teacher.setPosition(rs.getString("position"));
                teacher.setSalary(rs.getDouble("salary"));
                teacher.setQualification(rs.getString("qualification"));
                teacher.setExperienceYears(rs.getInt("experience_years"));
                user = teacher;
                break;
                
            case ADMIN:
                user = new Admin();
                break;
        }
        
        if (user != null) {
            user.setUserId(rs.getString("user_id"));
            user.setUsername(rs.getString("username"));
            user.setPassword(rs.getString("password"));
            user.setFullName(rs.getString("full_name"));
            user.setEmail(rs.getString("email"));
            user.setPhone(rs.getString("phone"));

            Date dob = rs.getDate("date_of_birth");
            if (dob != null) {
                user.setDateOfBirth(dob.toLocalDate());
            }

            user.setAddress(rs.getString("address"));
            user.setRole(role);

            // Load teachingCourses cho Teacher
            if (user instanceof Teacher) {
                loadTeachingCourses((Teacher) user);
            }

            // Load enrolledCourses cho Student
            if (user instanceof Student) {
                loadEnrolledCourses((Student) user);
            }
        }

        return user;
    }
    
    /**
     * Thêm chi tiết sinh viên
     */
    private void addStudentDetails(Connection conn, Student student) throws SQLException {
        String sql = """
            INSERT INTO Students (student_id, user_id, class_name, major, year_of_study, gpa)
            VALUES (?, ?, ?, ?, ?, ?)
            """;
        
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, student.getStudentId());
        stmt.setString(2, student.getUserId());
        stmt.setString(3, student.getClassName());
        stmt.setString(4, student.getMajor());
        stmt.setInt(5, student.getYear());
        stmt.setDouble(6, student.getGpa());
        
        stmt.executeUpdate();
    }
    
    /**
     * Thêm chi tiết giáo viên
     */
    private void addTeacherDetails(Connection conn, Teacher teacher) throws SQLException {
        String sql = """
            INSERT INTO Teachers (teacher_id, user_id, department, position, salary, qualification, experience_years)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """;

        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, teacher.getTeacherId());
        stmt.setString(2, teacher.getUserId());
        stmt.setString(3, teacher.getDepartment());
        stmt.setString(4, teacher.getPosition());
        stmt.setDouble(5, teacher.getSalary());
        stmt.setString(6, teacher.getQualification());
        stmt.setInt(7, teacher.getExperienceYears());

        stmt.executeUpdate();

        // Lưu danh sách môn dạy
        saveTeachingCourses(conn, teacher);
    }
    
    /**
     * Cập nhật chi tiết sinh viên
     */
    private void updateStudentDetails(Connection conn, Student student) throws SQLException {
        String sql = """
            UPDATE Students SET class_name = ?, major = ?, year_of_study = ?, gpa = ?
            WHERE student_id = ?
            """;
        
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, student.getClassName());
        stmt.setString(2, student.getMajor());
        stmt.setInt(3, student.getYear());
        stmt.setDouble(4, student.getGpa());
        stmt.setString(5, student.getStudentId());
        
        stmt.executeUpdate();
    }
    
    /**
     * Cập nhật chi tiết giáo viên
     */
    private void updateTeacherDetails(Connection conn, Teacher teacher) throws SQLException {
        String sql = """
            UPDATE Teachers SET department = ?, position = ?, salary = ?, qualification = ?, experience_years = ?
            WHERE teacher_id = ?
            """;

        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, teacher.getDepartment());
        stmt.setString(2, teacher.getPosition());
        stmt.setDouble(3, teacher.getSalary());
        stmt.setString(4, teacher.getQualification());
        stmt.setInt(5, teacher.getExperienceYears());
        stmt.setString(6, teacher.getTeacherId());

        stmt.executeUpdate();

        // Cập nhật danh sách môn dạy
        updateTeachingCourses(conn, teacher);
    }
    
    /**
     * Lưu danh sách môn dạy của giáo viên
     */
    private void saveTeachingCourses(Connection conn, Teacher teacher) throws SQLException {
        if (teacher.getTeachingCourses() == null || teacher.getTeachingCourses().isEmpty()) {
            return;
        }

        String sql = "INSERT INTO Teacher_Courses (teacher_id, course_name) VALUES (?, ?)";
        PreparedStatement stmt = conn.prepareStatement(sql);

        for (String courseName : teacher.getTeachingCourses()) {
            if (courseName != null && !courseName.trim().isEmpty()) {
                stmt.setString(1, teacher.getTeacherId());
                stmt.setString(2, courseName.trim());
                try {
                    stmt.executeUpdate();

                    // Tự động tạo hoặc cập nhật môn học trong bảng Courses
                    createOrUpdateCourseForTeacher(conn, teacher, courseName.trim());

                } catch (SQLException e) {
                    // Ignore duplicate entries
                    if (!e.getMessage().contains("UNIQUE")) {
                        throw e;
                    }
                }
            }
        }
    }

    /**
     * Cập nhật danh sách môn dạy của giáo viên
     */
    private void updateTeachingCourses(Connection conn, Teacher teacher) throws SQLException {
        // Lấy danh sách môn dạy cũ để xử lý
        List<String> oldCourses = getCurrentTeachingCourses(conn, teacher.getTeacherId());

        // Xóa tất cả môn dạy hiện tại
        String deleteSql = "DELETE FROM Teacher_Courses WHERE teacher_id = ?";
        PreparedStatement deleteStmt = conn.prepareStatement(deleteSql);
        deleteStmt.setString(1, teacher.getTeacherId());
        deleteStmt.executeUpdate();

        // Cập nhật teacher_id = null cho các môn học cũ (nếu không có giảng viên khác)
        for (String oldCourse : oldCourses) {
            removeTeacherFromCourseIfNoOtherTeacher(conn, oldCourse, teacher.getTeacherId());
        }

        // Thêm lại danh sách môn dạy mới
        saveTeachingCourses(conn, teacher);
    }

    /**
     * Lấy danh sách môn dạy hiện tại của giảng viên
     */
    private List<String> getCurrentTeachingCourses(Connection conn, String teacherId) throws SQLException {
        String sql = "SELECT course_name FROM Teacher_Courses WHERE teacher_id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, teacherId);
        ResultSet rs = stmt.executeQuery();

        List<String> courses = new ArrayList<>();
        while (rs.next()) {
            courses.add(rs.getString("course_name"));
        }
        return courses;
    }

    /**
     * Xóa giảng viên khỏi môn học nếu không có giảng viên khác dạy môn này
     */
    private void removeTeacherFromCourseIfNoOtherTeacher(Connection conn, String courseName, String currentTeacherId) throws SQLException {
        // Kiểm tra xem có giảng viên khác dạy môn này không
        String checkSql = "SELECT COUNT(*) FROM Teacher_Courses WHERE course_name = ? AND teacher_id != ?";
        PreparedStatement checkStmt = conn.prepareStatement(checkSql);
        checkStmt.setString(1, courseName);
        checkStmt.setString(2, currentTeacherId);
        ResultSet rs = checkStmt.executeQuery();
        rs.next();

        if (rs.getInt(1) == 0) {
            // Không có giảng viên khác, cập nhật teacher_id = null
            String updateSql = "UPDATE Courses SET teacher_id = NULL, teacher_name = NULL WHERE course_name = ?";
            PreparedStatement updateStmt = conn.prepareStatement(updateSql);
            updateStmt.setString(1, courseName);
            updateStmt.executeUpdate();

            System.out.println("⚠️ Xóa giảng viên khỏi môn học: " + courseName);
        }
    }

    /**
     * Tạo hoặc cập nhật môn học trong bảng Courses khi thêm giảng viên
     */
    private void createOrUpdateCourseForTeacher(Connection conn, Teacher teacher, String courseName) throws SQLException {
        // Kiểm tra xem môn học đã tồn tại chưa
        String checkSql = "SELECT course_id, teacher_id FROM Courses WHERE course_name = ?";
        PreparedStatement checkStmt = conn.prepareStatement(checkSql);
        checkStmt.setString(1, courseName);
        ResultSet rs = checkStmt.executeQuery();

        if (rs.next()) {
            // Môn học đã tồn tại, cập nhật teacher nếu chưa có hoặc khác
            String existingTeacherId = rs.getString("teacher_id");
            if (existingTeacherId == null || existingTeacherId.isEmpty()) {
                // Cập nhật teacher cho môn học
                String updateSql = "UPDATE Courses SET teacher_id = ?, teacher_name = ? WHERE course_name = ?";
                PreparedStatement updateStmt = conn.prepareStatement(updateSql);
                updateStmt.setString(1, teacher.getTeacherId());
                updateStmt.setString(2, teacher.getFullName());
                updateStmt.setString(3, courseName);
                updateStmt.executeUpdate();

                System.out.println("✅ Cập nhật giảng viên cho môn học: " + courseName + " → " + teacher.getFullName());
            }
        } else {
            // Môn học chưa tồn tại, tạo mới
            String courseId = generateCourseId(courseName);
            String insertSql = """
                INSERT INTO Courses (course_id, course_name, description, credits, teacher_id, teacher_name,
                                   schedule, classroom, max_students)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

            PreparedStatement insertStmt = conn.prepareStatement(insertSql);
            insertStmt.setString(1, courseId);
            insertStmt.setString(2, courseName);
            insertStmt.setString(3, "Môn học được tạo tự động khi thêm giảng viên");
            insertStmt.setInt(4, 3); // Default 3 credits
            insertStmt.setString(5, teacher.getTeacherId());
            insertStmt.setString(6, teacher.getFullName());
            insertStmt.setString(7, "Chưa xếp lịch");
            insertStmt.setString(8, "Chưa xếp phòng");
            insertStmt.setInt(9, 50); // Default max 50 students

            insertStmt.executeUpdate();

            System.out.println("✅ Tạo môn học mới: " + courseName + " với giảng viên " + teacher.getFullName());
        }
    }

    /**
     * Tạo course ID tự động từ tên môn học
     */
    private String generateCourseId(String courseName) {
        // Tạo ID từ tên môn học
        String baseId = courseName.replaceAll("[^a-zA-Z0-9]", "").toUpperCase();
        if (baseId.length() > 10) {
            baseId = baseId.substring(0, 10);
        }

        // Thêm số thứ tự nếu trùng
        String finalId = baseId;
        int counter = 1;

        try {
            Connection conn = DatabaseConnection.getConnection();
            String checkSql = "SELECT COUNT(*) FROM Courses WHERE course_id = ?";
            PreparedStatement stmt = conn.prepareStatement(checkSql);

            while (true) {
                stmt.setString(1, finalId);
                ResultSet rs = stmt.executeQuery();
                rs.next();

                if (rs.getInt(1) == 0) {
                    break; // ID không trùng
                }

                finalId = baseId + counter;
                counter++;
            }

        } catch (SQLException e) {
            System.err.println("❌ Lỗi generate course ID: " + e.getMessage());
            finalId = baseId + System.currentTimeMillis() % 1000;
        }

        return finalId;
    }

    /**
     * Load danh sách môn dạy của giáo viên từ database
     */
    private void loadTeachingCourses(Teacher teacher) {
        String sql = "SELECT course_name FROM Teacher_Courses WHERE teacher_id = ? ORDER BY course_name";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, teacher.getTeacherId());

            ResultSet rs = stmt.executeQuery();
            List<String> teachingCourses = new ArrayList<>();

            while (rs.next()) {
                teachingCourses.add(rs.getString("course_name"));
            }

            teacher.setTeachingCourses(teachingCourses);

        } catch (SQLException e) {
            System.err.println("❌ Lỗi load teaching courses cho teacher " + teacher.getTeacherId() + ": " + e.getMessage());
            teacher.setTeachingCourses(new ArrayList<>());
        }
    }

    /**
     * Load danh sách môn học đã đăng ký của sinh viên từ database
     */
    private void loadEnrolledCourses(Student student) {
        String sql = "SELECT course_id FROM Course_Enrollments WHERE student_id = ? ORDER BY course_id";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, student.getStudentId());

            ResultSet rs = stmt.executeQuery();
            List<String> enrolledCourses = new ArrayList<>();

            while (rs.next()) {
                enrolledCourses.add(rs.getString("course_id"));
            }

            student.setEnrolledCourses(enrolledCourses);

            System.out.println("📚 Loaded " + enrolledCourses.size() + " enrolled courses for student " + student.getStudentId() + ": " + enrolledCourses);

        } catch (SQLException e) {
            System.err.println("❌ Lỗi load enrolled courses cho student " + student.getStudentId() + ": " + e.getMessage());
            student.setEnrolledCourses(new ArrayList<>());
        }
    }

    /**
     * Tạo ID mới cho user (đảm bảo unique)
     */
    public String generateNextUserId(User.UserRole role) {
        String prefix = switch (role) {
            case ADMIN -> "ADM";
            case TEACHER -> "TCH";
            case STUDENT -> "STU";
        };

        // Tìm số thứ tự cao nhất hiện có
        String sql = "SELECT MAX(CAST(SUBSTRING(user_id, 4, 3) AS INT)) FROM Users WHERE user_id LIKE ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, prefix + "%");

            ResultSet rs = stmt.executeQuery();
            int maxNumber = 0;
            if (rs.next()) {
                maxNumber = rs.getInt(1);
            }

            // Tạo ID mới và kiểm tra xem có tồn tại không
            String newId;
            int nextNumber = maxNumber + 1;
            do {
                newId = prefix + String.format("%03d", nextNumber);
                nextNumber++;
            } while (isUserIdExists(newId));

            return newId;

        } catch (SQLException e) {
            System.err.println("❌ Lỗi tạo ID: " + e.getMessage());

            // Fallback: tìm ID đầu tiên available
            for (int i = 1; i <= 999; i++) {
                String fallbackId = prefix + String.format("%03d", i);
                if (!isUserIdExists(fallbackId)) {
                    return fallbackId;
                }
            }
        }

        return prefix + "001";
    }
}

package com.example.doancuoikyjava.service;

import javafx.stage.DirectoryChooser;
import javafx.stage.Stage;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service for downloading files from chat messages
 */
public class FileDownloadService {
    
    private static final String DOWNLOADS_DIR = "downloads/";
    
    public FileDownloadService() {
        initializeDownloadsDirectory();
    }
    
    /**
     * Initialize downloads directory
     */
    private void initializeDownloadsDirectory() {
        try {
            Files.createDirectories(Paths.get(DOWNLOADS_DIR));
            System.out.println("📥 Downloads directory initialized successfully");
        } catch (IOException e) {
            System.err.println("❌ Error creating downloads directory: " + e.getMessage());
        }
    }
    
    /**
     * Download file to default downloads folder
     */
    public DownloadResult downloadFile(String sourceFilePath, String originalFileName) {
        try {
            File sourceFile = new File(sourceFilePath);
            if (!sourceFile.exists()) {
                return new DownloadResult(false, "❌ File không tồn tại: " + originalFileName, null);
            }
            
            // Generate unique filename for download
            String downloadFileName = generateDownloadFileName(originalFileName);
            Path downloadPath = Paths.get(DOWNLOADS_DIR + downloadFileName);
            
            // Copy file to downloads
            Files.copy(sourceFile.toPath(), downloadPath, StandardCopyOption.REPLACE_EXISTING);
            
            System.out.println("📥 File downloaded: " + downloadFileName);
            return new DownloadResult(true, "✅ Tải xuống thành công: " + originalFileName, downloadPath.toString());
            
        } catch (IOException e) {
            System.err.println("❌ Error downloading file: " + e.getMessage());
            return new DownloadResult(false, "❌ Lỗi tải xuống: " + e.getMessage(), null);
        }
    }
    
    /**
     * Download file with user-selected location
     */
    public DownloadResult downloadFileWithDialog(String sourceFilePath, String originalFileName, Stage parentStage) {
        try {
            File sourceFile = new File(sourceFilePath);
            if (!sourceFile.exists()) {
                return new DownloadResult(false, "❌ File không tồn tại: " + originalFileName, null);
            }
            
            // Show directory chooser
            DirectoryChooser directoryChooser = new DirectoryChooser();
            directoryChooser.setTitle("📁 Chọn thư mục lưu file");
            directoryChooser.setInitialDirectory(new File(System.getProperty("user.home")));
            
            File selectedDirectory = directoryChooser.showDialog(parentStage);
            if (selectedDirectory == null) {
                return new DownloadResult(false, "❌ Người dùng hủy tải xuống", null);
            }
            
            // Generate unique filename
            String downloadFileName = generateDownloadFileName(originalFileName);
            Path downloadPath = Paths.get(selectedDirectory.getAbsolutePath(), downloadFileName);
            
            // Copy file
            Files.copy(sourceFile.toPath(), downloadPath, StandardCopyOption.REPLACE_EXISTING);
            
            System.out.println("📥 File downloaded to: " + downloadPath.toString());
            return new DownloadResult(true, "✅ Tải xuống thành công: " + originalFileName, downloadPath.toString());
            
        } catch (IOException e) {
            System.err.println("❌ Error downloading file: " + e.getMessage());
            return new DownloadResult(false, "❌ Lỗi tải xuống: " + e.getMessage(), null);
        }
    }
    
    /**
     * Open file with default system application
     */
    public boolean openFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                System.err.println("❌ File không tồn tại: " + filePath);
                return false;
            }
            
            // Try to open with default application
            if (java.awt.Desktop.isDesktopSupported()) {
                java.awt.Desktop desktop = java.awt.Desktop.getDesktop();
                if (desktop.isSupported(java.awt.Desktop.Action.OPEN)) {
                    desktop.open(file);
                    System.out.println("📂 Opened file: " + file.getName());
                    return true;
                }
            }
            
            System.err.println("❌ Desktop không hỗ trợ mở file");
            return false;
            
        } catch (Exception e) {
            System.err.println("❌ Error opening file: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get file size in human readable format
     */
    public String getFileSizeFormatted(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) return "0 B";
            
            long size = file.length();
            if (size < 1024) {
                return size + " B";
            } else if (size < 1024 * 1024) {
                return String.format("%.1f KB", size / 1024.0);
            } else {
                return String.format("%.1f MB", size / (1024.0 * 1024.0));
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    /**
     * Check if file exists
     */
    public boolean fileExists(String filePath) {
        try {
            return new File(filePath).exists();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Generate unique download filename
     */
    private String generateDownloadFileName(String originalFileName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        
        // Extract extension
        String extension = "";
        int lastDot = originalFileName.lastIndexOf('.');
        if (lastDot > 0) {
            extension = originalFileName.substring(lastDot);
            originalFileName = originalFileName.substring(0, lastDot);
        }
        
        return String.format("%s_%s%s", originalFileName, timestamp, extension);
    }
    
    /**
     * Get file icon based on extension
     */
    public String getFileIcon(String fileName) {
        if (fileName == null) return "📄";
        
        String extension = "";
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            extension = fileName.substring(lastDot + 1).toLowerCase();
        }
        
        switch (extension) {
            case "pdf": return "📕";
            case "doc":
            case "docx": return "📘";
            case "xls":
            case "xlsx": return "📗";
            case "ppt":
            case "pptx": return "📙";
            case "txt": return "📄";
            case "jpg":
            case "jpeg":
            case "png":
            case "gif": return "🖼️";
            case "mp3":
            case "wav":
            case "m4a": return "🎵";
            case "mp4":
            case "avi": return "🎬";
            case "zip":
            case "rar": return "🗜️";
            default: return "📎";
        }
    }
    
    /**
     * Download result class
     */
    public static class DownloadResult {
        private final boolean success;
        private final String message;
        private final String downloadPath;
        
        public DownloadResult(boolean success, String message, String downloadPath) {
            this.success = success;
            this.message = message;
            this.downloadPath = downloadPath;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getDownloadPath() { return downloadPath; }
    }
}

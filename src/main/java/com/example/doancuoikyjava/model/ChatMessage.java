package com.example.doancuoikyjava.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class for chat messages between students and teachers
 */
public class ChatMessage {
    
    public enum MessageType {
        TEXT,           // Regular text message
        IMAGE,          // Image file
        AUDIO,          // Audio recording
        FILE,           // Document/file attachment
        TEXT_WITH_IMAGE, // Text message with attached image
        TEXT_WITH_FILE,  // Text message with attached file
        JOIN,           // User joined chat
        LEAVE,          // User left chat
        TYPING,         // User is typing
        SYSTEM          // System message
    }
    
    private String messageId;
    private String senderId;
    private String senderName;
    private String receiverId;
    private String receiverName;
    private String content;
    private MessageType type;
    private LocalDateTime timestamp;
    private boolean isRead;
    private String courseId;        // Optional: for course-specific chats
    private String courseName;      // Optional: for course-specific chats

    // File and media properties
    private String fileName;        // Original file name
    private String filePath;        // Path to stored file
    private String fileType;        // MIME type
    private long fileSize;          // File size in bytes
    private String thumbnailPath;   // For images - path to thumbnail
    private int audioDuration;      // For audio - duration in seconds
    
    // Constructors
    public ChatMessage() {
        this.timestamp = LocalDateTime.now();
        this.isRead = false;
        this.type = MessageType.TEXT;
    }
    
    public ChatMessage(String senderId, String senderName, String receiverId, String receiverName, String content) {
        this();
        this.senderId = senderId;
        this.senderName = senderName;
        this.receiverId = receiverId;
        this.receiverName = receiverName;
        this.content = content;
        this.messageId = generateMessageId();
    }
    
    public ChatMessage(String senderId, String senderName, String receiverId, String receiverName,
                      String content, MessageType type) {
        this(senderId, senderName, receiverId, receiverName, content);
        this.type = type;
    }

    // Constructor for file messages
    public ChatMessage(String senderId, String senderName, String receiverId, String receiverName,
                      String content, MessageType type, String fileName, String filePath, String fileType, long fileSize) {
        this(senderId, senderName, receiverId, receiverName, content, type);
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileType = fileType;
        this.fileSize = fileSize;
    }
    
    // Generate unique message ID
    private String generateMessageId() {
        return "MSG_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    // Getters and Setters
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public String getSenderId() {
        return senderId;
    }
    
    public void setSenderId(String senderId) {
        this.senderId = senderId;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    
    public String getReceiverId() {
        return receiverId;
    }
    
    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }
    
    public String getReceiverName() {
        return receiverName;
    }
    
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public MessageType getType() {
        return type;
    }
    
    public void setType(MessageType type) {
        this.type = type;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isRead() {
        return isRead;
    }
    
    public void setRead(boolean read) {
        isRead = read;
    }
    
    public String getCourseId() {
        return courseId;
    }
    
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    // File and media getters and setters
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getThumbnailPath() {
        return thumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    public int getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(int audioDuration) {
        this.audioDuration = audioDuration;
    }

    // Utility methods for file handling
    public boolean hasFile() {
        return fileName != null && !fileName.trim().isEmpty();
    }

    public boolean isImageMessage() {
        return type == MessageType.IMAGE;
    }

    public boolean isAudioMessage() {
        return type == MessageType.AUDIO;
    }

    public boolean isFileMessage() {
        return type == MessageType.FILE;
    }

    public boolean isTextWithImageMessage() {
        return type == MessageType.TEXT_WITH_IMAGE;
    }

    public boolean isTextWithFileMessage() {
        return type == MessageType.TEXT_WITH_FILE;
    }

    public boolean isComboMessage() {
        return type == MessageType.TEXT_WITH_IMAGE || type == MessageType.TEXT_WITH_FILE;
    }

    public boolean hasTextContent() {
        return content != null && !content.trim().isEmpty();
    }

    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    public String getFormattedAudioDuration() {
        if (audioDuration <= 0) return "0:00";
        int minutes = audioDuration / 60;
        int seconds = audioDuration % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    // Utility methods
    public String getFormattedTimestamp() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        return timestamp.format(formatter);
    }
    
    public String getTimeOnly() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return timestamp.format(formatter);
    }
    
    public boolean isSentByUser(String userId) {
        return senderId != null && senderId.equals(userId);
    }
    
    public boolean isReceivedByUser(String userId) {
        return receiverId != null && receiverId.equals(userId);
    }
    
    public boolean involvesUser(String userId) {
        return isSentByUser(userId) || isReceivedByUser(userId);
    }
    
    public String getOtherUserId(String currentUserId) {
        if (isSentByUser(currentUserId)) {
            return receiverId;
        } else if (isReceivedByUser(currentUserId)) {
            return senderId;
        }
        return null;
    }
    
    public String getOtherUserName(String currentUserId) {
        if (isSentByUser(currentUserId)) {
            return receiverName;
        } else if (isReceivedByUser(currentUserId)) {
            return senderName;
        }
        return null;
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s -> %s: %s", 
                           getFormattedTimestamp(), 
                           senderName, 
                           receiverName, 
                           content);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ChatMessage that = (ChatMessage) obj;
        return messageId != null ? messageId.equals(that.messageId) : that.messageId == null;
    }
    
    @Override
    public int hashCode() {
        return messageId != null ? messageId.hashCode() : 0;
    }
}

package com.example.doancuoikyjava.model;

import java.time.LocalDate;

public class Grade {
    private String gradeId;
    private String studentId;
    private String courseId;
    private String courseName;
    private double score;
    private String letterGrade;
    private double gradePoint;
    private int credits;
    private LocalDate dateRecorded;
    private String semester;
    private String teacherId;

    public Grade() {}

    public Grade(String gradeId, String studentId, String courseId, String courseName,
                 double score, int credits, String semester, String teacherId) {
        this.gradeId = gradeId;
        this.studentId = studentId;
        this.courseId = courseId;
        this.courseName = courseName;
        this.score = score;
        this.credits = credits;
        this.semester = semester;
        this.teacherId = teacherId;
        this.dateRecorded = LocalDate.now();
        calculateLetterGrade();
    }

    // Getters and Setters
    public String getGradeId() { return gradeId; }
    public void setGradeId(String gradeId) { this.gradeId = gradeId; }

    public String getStudentId() { return studentId; }
    public void setStudentId(String studentId) { this.studentId = studentId; }

    public String getCourseId() { return courseId; }
    public void setCourseId(String courseId) { this.courseId = courseId; }

    public String getCourseName() { return courseName; }
    public void setCourseName(String courseName) { this.courseName = courseName; }

    public double getScore() { return score; }
    public void setScore(double score) { 
        this.score = score; 
        calculateLetterGrade();
    }

    public String getLetterGrade() { return letterGrade; }
    public void setLetterGrade(String letterGrade) { this.letterGrade = letterGrade; }

    public double getGradePoint() { return gradePoint; }
    public void setGradePoint(double gradePoint) { this.gradePoint = gradePoint; }

    public int getCredits() { return credits; }
    public void setCredits(int credits) { this.credits = credits; }

    public LocalDate getDateRecorded() { return dateRecorded; }
    public void setDateRecorded(LocalDate dateRecorded) { this.dateRecorded = dateRecorded; }

    public String getSemester() { return semester; }
    public void setSemester(String semester) { this.semester = semester; }

    public String getTeacherId() { return teacherId; }
    public void setTeacherId(String teacherId) { this.teacherId = teacherId; }

    // Utility method to calculate letter grade and grade point (thang điểm 0-10)
    private void calculateLetterGrade() {
        if (score >= 9.0) {
            letterGrade = "A+";
            gradePoint = 4.0;
        } else if (score >= 8.5) {
            letterGrade = "A";
            gradePoint = 3.7;
        } else if (score >= 8.0) {
            letterGrade = "B+";
            gradePoint = 3.5;
        } else if (score >= 7.0) {
            letterGrade = "B";
            gradePoint = 3.0;
        } else if (score >= 6.5) {
            letterGrade = "C+";
            gradePoint = 2.5;
        } else if (score >= 5.5) {
            letterGrade = "C";
            gradePoint = 2.0;
        } else if (score >= 4.0) {
            letterGrade = "D";
            gradePoint = 1.0;
        } else {
            letterGrade = "F";
            gradePoint = 0.0;
        }
    }

    @Override
    public String toString() {
        return "Grade{" +
                "courseId='" + courseId + '\'' +
                ", courseName='" + courseName + '\'' +
                ", score=" + score +
                ", letterGrade='" + letterGrade + '\'' +
                ", credits=" + credits +
                ", semester='" + semester + '\'' +
                '}';
    }
}

<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.effect.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherGradesController">
   <top>
      <!-- Modern Header with Gradient -->
      <VBox styleClass="modern-teacher-header" spacing="0">
         <children>
            <!-- Navigation Bar -->
            <HBox alignment="CENTER_LEFT" styleClass="modern-nav-bar" spacing="25.0">
               <children>
                  <Button fx:id="backButton" onAction="#goBack" styleClass="modern-nav-button,back-button" text="← Quay lại" />
                  <VBox spacing="3.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="🎓" styleClass="header-icon">
                                 <font>
                                    <Font size="24.0" />
                                 </font>
                              </Label>
                              <Label text="QUẢN LÝ ĐIỂM SỐ" styleClass="modern-header-title">
                                 <font>
                                    <Font name="System Bold" size="20.0" />
                                 </font>
                              </Label>
                           </children>
                        </HBox>
                        <Label text="Hệ thống quản lý điểm cho giảng viên" styleClass="modern-header-subtitle">
                           <font>
                              <Font size="12.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <VBox alignment="CENTER_RIGHT" spacing="5.0">
                     <children>
                        <Label fx:id="welcomeLabel" text="Xin chào, Giáo viên" styleClass="modern-welcome-label">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        <Label text="Thang điểm 0-10" styleClass="scale-indicator">
                           <font>
                              <Font size="11.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="25.0" right="25.0" top="20.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>
   <center>
      <VBox styleClass="modern-content-container" spacing="25.0">
         <children>
            <!-- Action Cards Section -->
            <VBox spacing="20.0" styleClass="action-section">
               <children>
                  <!-- Quick Actions Card -->
                  <HBox spacing="20.0" styleClass="quick-actions-card">
                     <children>
                        <VBox spacing="8.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="Thao tác nhanh" styleClass="section-title">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                              <Label text="Nhập điểm và quản lý dữ liệu" styleClass="section-subtitle">
                                 <font>
                                    <Font size="12.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        <Region HBox.hgrow="ALWAYS" />
                        <HBox spacing="15.0" alignment="CENTER_RIGHT">
                           <children>
                              <Button fx:id="addGradeBtn" onAction="#showAddGradeDialog"
                                     styleClass="modern-action-btn,primary-action" text="✨ Nhập điểm mới">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Button>
                              <Button fx:id="refreshBtn" onAction="#refreshData"
                                     styleClass="modern-action-btn,secondary-action" text="🔄 Làm mới">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Button>
                           </children>
                        </HBox>
                     </children>
                  </HBox>

                  <!-- Filter Card -->
                  <VBox spacing="15.0" styleClass="filter-card">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="🔍" styleClass="filter-icon">
                                 <font>
                                    <Font size="18.0" />
                                 </font>
                              </Label>
                              <Label text="Bộ lọc và tìm kiếm" styleClass="filter-title">
                                 <font>
                                    <Font name="System Bold" size="15.0" />
                                 </font>
                              </Label>
                           </children>
                        </HBox>
                        <HBox spacing="15.0" alignment="CENTER_LEFT">
                           <children>
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Môn học" styleClass="filter-label" />
                                    <ComboBox fx:id="courseFilterComboBox" promptText="🔍 Chọn môn học..."
                                             styleClass="modern-filter-combo" prefWidth="180.0" />
                                 </children>
                              </VBox>
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Học kỳ" styleClass="filter-label" />
                                    <ComboBox fx:id="semesterFilterComboBox" promptText="🔍 Chọn học kỳ..."
                                             styleClass="modern-filter-combo" prefWidth="150.0" />
                                 </children>
                              </VBox>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Tìm kiếm sinh viên" styleClass="filter-label" />
                                    <TextField fx:id="searchField" promptText="🔍 Nhập tên hoặc MSSV..."
                                              styleClass="modern-search-field" />
                                 </children>
                              </VBox>
                              <VBox spacing="5.0" alignment="BOTTOM_CENTER">
                                 <children>
                                    <Label text=" " />
                                    <Button fx:id="searchBtn" onAction="#searchGrades"
                                           styleClass="modern-search-btn" text="🔍 Tìm kiếm">
                                       <font>
                                          <Font name="System Bold" size="13.0" />
                                       </font>
                                    </Button>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Modern Table Card -->
            <VBox spacing="15.0" styleClass="table-card">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="📊" styleClass="table-icon">
                           <font>
                              <Font size="18.0" />
                           </font>
                        </Label>
                        <Label text="Danh sách điểm số" styleClass="table-title">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                     </children>
                  </HBox>

                  <TableView fx:id="gradesTableView" VBox.vgrow="ALWAYS" styleClass="modern-table">
                     <columns>
                        <TableColumn fx:id="studentIdColumn" prefWidth="100.0" text="MSSV" styleClass="table-column-header" />
                        <TableColumn fx:id="studentNameColumn" prefWidth="200.0" text="👨‍🎓 Tên sinh viên" styleClass="table-column-header" />
                        <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="📚 Môn học" styleClass="table-column-header" />
                        <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" styleClass="table-column-header" />
                        <TableColumn fx:id="scoreColumn" prefWidth="80.0" text="⭐ Điểm số" styleClass="table-column-header" />
                        <TableColumn fx:id="letterGradeColumn" prefWidth="80.0" text="🏆 Xếp loại" styleClass="table-column-header" />
                        <TableColumn fx:id="semesterColumn" prefWidth="100.0" text="📅 Học kỳ" styleClass="table-column-header" />
                        <TableColumn fx:id="dateColumn" prefWidth="120.0" text="📝 Ngày nhập" styleClass="table-column-header" />
                        <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="⚙️ Thao tác" styleClass="table-column-header" />
                     </columns>
                  </TableView>
               </children>
            </VBox>

            <!-- Statistics and Export Card -->
            <HBox spacing="20.0" styleClass="stats-card">
               <children>
                  <!-- Statistics Section -->
                  <HBox spacing="25.0" alignment="CENTER_LEFT">
                     <children>
                        <VBox spacing="5.0" alignment="CENTER" styleClass="stat-item">
                           <children>
                              <Label text="📈" styleClass="stat-icon">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label fx:id="totalGradesLabel" text="0" styleClass="stat-number">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <Label text="Tổng điểm" styleClass="stat-label">
                                 <font>
                                    <Font size="11.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>

                        <Separator orientation="VERTICAL" styleClass="stat-separator" />

                        <VBox spacing="5.0" alignment="CENTER" styleClass="stat-item">
                           <children>
                              <Label text="🎯" styleClass="stat-icon">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label fx:id="averageGradeLabel" text="0.00" styleClass="stat-number">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <Label text="Điểm TB" styleClass="stat-label">
                                 <font>
                                    <Font size="11.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                  </HBox>

                  <Region HBox.hgrow="ALWAYS" />

                  <!-- Export Section -->
                  <VBox spacing="8.0" alignment="CENTER_RIGHT">
                     <children>
                        <Label text="Xuất dữ liệu" styleClass="export-title">
                           <font>
                              <Font name="System Bold" size="13.0" />
                           </font>
                        </Label>
                        <Button fx:id="exportBtn" onAction="#exportGrades"
                               styleClass="modern-export-btn" text="📊 Xuất Excel">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Button>
                     </children>
                  </VBox>
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>

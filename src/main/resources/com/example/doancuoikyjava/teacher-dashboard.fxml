<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Label styleClass="nav-button" text="👨‍🏫 GIÁO VIÊN">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Giáo viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
            <Button fx:id="logoutButton" onAction="#handleLogout" styleClass="nav-button" text="Đăng xuất" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <left>
      <VBox styleClass="sidebar" spacing="10.0">
         <children>
            <Label styleClass="sidebar-button,active" text="📊 Tổng quan">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <Button fx:id="myCoursesBtn" onAction="#showMyCourses" styleClass="sidebar-button" text="📚 Môn học của tôi" />
            <Button fx:id="studentsBtn" onAction="#showStudents" styleClass="sidebar-button" text="👨‍🎓 Danh sách sinh viên" />
            <Button fx:id="gradesBtn" onAction="#showGrades" styleClass="sidebar-button" text="📝 Quản lý điểm" />
            <Button fx:id="scheduleBtn" onAction="#showSchedule" styleClass="sidebar-button" text="📅 Lịch giảng dạy" />
            <Button fx:id="notificationsBtn" onAction="#showNotifications" styleClass="sidebar-button" text="📢 Thông báo" />
            <Button fx:id="chatBtn" onAction="#showChat" styleClass="sidebar-button" text="💬 Chat với SV" />
            <Button fx:id="colleagueChatBtn" onAction="#showColleagueChat" styleClass="sidebar-button" text="👥 Chat đồng nghiệp" />
            <Button fx:id="profileBtn" onAction="#showProfile" styleClass="sidebar-button" text="👤 Thông tin cá nhân" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </left>
   <center>
      <ScrollPane fitToWidth="true" styleClass="content-container">
         <content>
            <VBox fx:id="contentArea" styleClass="dashboard-container" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Bảng điều khiển giáo viên">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  
                  <HBox spacing="20.0">
                     <children>
                        <VBox styleClass="stats-card">
                           <children>
                              <Label fx:id="totalCoursesLabel" styleClass="stats-number" text="0">
                                 <font>
                                    <Font name="System Bold" size="36.0" />
                                 </font>
                              </Label>
                              <Label styleClass="stats-label" text="MÔN HỌC">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <VBox styleClass="stats-card">
                           <children>
                              <Label fx:id="totalStudentsLabel" styleClass="stats-number" text="0">
                                 <font>
                                    <Font name="System Bold" size="36.0" />
                                 </font>
                              </Label>
                              <Label styleClass="stats-label" text="SINH VIÊN">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <VBox styleClass="stats-card">
                           <children>
                              <Label fx:id="pendingGradesLabel" styleClass="stats-number" text="0">
                                 <font>
                                    <Font name="System Bold" size="36.0" />
                                 </font>
                              </Label>
                              <Label styleClass="stats-label" text="ĐIỂM CHỜ NHẬP">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <HBox spacing="20.0">
                     <children>
                        <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="card-header" text="Môn học đang giảng dạy">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <TableView fx:id="coursesTableView" prefHeight="200.0">
                                 <columns>
                                    <TableColumn fx:id="courseIdColumn" prefWidth="80.0" text="Mã MH" />
                                    <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="Tên môn học" />
                                    <TableColumn fx:id="creditsColumn" prefWidth="60.0" text="Tín chỉ" />
                                    <TableColumn fx:id="studentsCountColumn" prefWidth="80.0" text="SV" />
                                    <TableColumn fx:id="scheduleColumn" prefWidth="150.0" text="Giờ học" />
                                 </columns>
                              </TableView>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <HBox spacing="20.0">
                     <children>
                        <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="card-header" text="Lịch giảng dạy hôm nay">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <ListView fx:id="todayScheduleListView" prefHeight="150.0" />
                           </children>
                        </VBox>
                        
                        <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="card-header" text="Thao tác nhanh">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <VBox spacing="10.0">
                                 <children>
                                    <Button onAction="#showGrades" styleClass="btn,btn-primary,full-width" text="📝 Nhập điểm" />
                                    <Button onAction="#showStudents" styleClass="btn,btn-success,full-width" text="👥 Xem danh sách lớp" />
                                    <Button onAction="#showSchedule" styleClass="btn,btn-warning,full-width" text="📅 Xem lịch dạy" />
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <VBox styleClass="card" spacing="15.0">
                     <children>
                        <Label styleClass="card-header" text="Thông báo và ghi chú">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <ListView fx:id="notificationsListView" prefHeight="120.0" />
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>

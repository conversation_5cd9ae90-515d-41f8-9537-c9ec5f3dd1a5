<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.StudentChatController">
   <top>
      <VBox style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
         <HBox alignment="CENTER_LEFT" spacing="15.0">
            <Button fx:id="backButton" onAction="#goBackToHome" text="🏠 Quay lại trang chủ"
                    style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 5; -fx-background-radius: 5; -fx-padding: 8 15; -fx-font-size: 12px; -fx-cursor: hand;" />

            <VBox>
               <Label text="💬 Chat với Giáo viên" textFill="WHITE">
                  <font>
                     <Font name="System Bold" size="24.0" />
                  </font>
               </Label>
               <Label text="Chọn giáo viên để bắt đầu trò chuyện" textFill="#E8E8E8">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
         </HBox>
      </VBox>
   </top>
   
   <left>
      <VBox prefWidth="300.0" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 0 1 0 0;">
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
         
         <Label text="👨‍🏫 Danh sách Giáo viên" style="-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #495057;">
            <VBox.margin>
               <Insets bottom="10.0" />
            </VBox.margin>
         </Label>
         
         <ListView fx:id="teacherListView" prefHeight="400.0" style="-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;">
            <VBox.margin>
               <Insets bottom="10.0" />
            </VBox.margin>
         </ListView>
         
         <VBox style="-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 10;">
            <Label text="ℹ️ Hướng dẫn:" style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #6c757d;">
               <VBox.margin>
                  <Insets bottom="5.0" />
               </VBox.margin>
            </Label>
            <Label text="• Chọn giáo viên từ danh sách" wrapText="true" style="-fx-font-size: 11px; -fx-text-fill: #6c757d;" />
            <Label text="• Chỉ hiển thị giáo viên của các môn bạn đã đăng ký" wrapText="true" style="-fx-font-size: 11px; -fx-text-fill: #6c757d;" />
            <Label text="• Tin nhắn được lưu trữ tự động" wrapText="true" style="-fx-font-size: 11px; -fx-text-fill: #6c757d;" />
         </VBox>
      </VBox>
   </left>
   
   <center>
      <VBox>
         <!-- Chat Header -->
         <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0; -fx-padding: 15;">
            <Label fx:id="chatHeaderLabel" text="Chọn giáo viên để bắt đầu chat" style="-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #495057;" />
            
            <Region HBox.hgrow="ALWAYS" />
            
            <HBox alignment="CENTER" spacing="5.0">
               <Label text="●" fx:id="onlineStatusLabel" style="-fx-font-size: 12px;" />
            </HBox>
         </HBox>
         
         <!-- Chat Area -->
         <ScrollPane fx:id="chatScrollPane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" VBox.vgrow="ALWAYS" style="-fx-background-color: #f8f9fa;">
            <VBox fx:id="chatArea" spacing="10.0" style="-fx-background-color: #f8f9fa;">
               <padding>
                  <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
               </padding>
            </VBox>
         </ScrollPane>
         
         <!-- Recording Status -->
         <HBox fx:id="recordingStatusBox" alignment="CENTER" style="-fx-background-color: #fff3cd; -fx-border-color: #ffeaa7; -fx-border-width: 1 0 0 0; -fx-padding: 10;" visible="false">
            <Label fx:id="recordingStatusLabel" text="🎤 Đang ghi âm..." style="-fx-text-fill: #856404; -fx-font-weight: bold;" />
         </HBox>

         <!-- Message Input with Enhanced Features -->
         <VBox style="-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1 0 0 0; -fx-padding: 15;">
            <!-- Attachment Preview Area -->
            <VBox fx:id="attachmentPreviewArea" spacing="5" style="-fx-padding: 0 0 10 0;" visible="false" />

            <!-- Attachment Buttons Row -->
            <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-padding: 0 0 10 0;">
               <Button fx:id="attachImageButton" text="🖼️" onAction="#attachImage"
                       style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 16px; -fx-cursor: hand;">
                  <tooltip>
                     <Tooltip text="Gửi ảnh" />
                  </tooltip>
               </Button>

               <Button fx:id="attachFileButton" text="📎" onAction="#attachFile"
                       style="-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 16px; -fx-cursor: hand;">
                  <tooltip>
                     <Tooltip text="Gửi file" />
                  </tooltip>
               </Button>

               <Button fx:id="recordAudioButton" text="🎤" onAction="#toggleAudioRecording"
                       style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-border-radius: 15; -fx-background-radius: 15; -fx-padding: 8 12; -fx-font-size: 16px; -fx-cursor: hand;">
                  <tooltip>
                     <Tooltip text="Ghi âm" />
                  </tooltip>
               </Button>

               <Region HBox.hgrow="ALWAYS" />

               <Label text="💡 Mẹo: Nhấn Enter để gửi tin nhắn" style="-fx-text-fill: #6c757d; -fx-font-size: 11px;" />
            </HBox>

            <!-- Message Input Row -->
            <HBox alignment="CENTER" spacing="10.0">
               <TextField fx:id="messageField" promptText="Nhập tin nhắn hoặc chọn file/ảnh/ghi âm..." HBox.hgrow="ALWAYS"
                          style="-fx-background-color: #f8f9fa; -fx-border-color: #ced4da; -fx-border-radius: 20; -fx-background-radius: 20; -fx-padding: 12 15; -fx-font-size: 14px;" />

               <Button fx:id="sendButton" text="📤 Gửi"
                       style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-border-radius: 20; -fx-background-radius: 20; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: bold; -fx-cursor: hand;" />
            </HBox>
         </VBox>
      </VBox>
   </center>
</BorderPane>

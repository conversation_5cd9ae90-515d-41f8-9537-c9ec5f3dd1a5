<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherColleagueChatController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="💬 CHAT ĐỒNG NGHIỆP">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Giáo viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <HBox styleClass="content-container">
         <children>
            <!-- Left Panel: Teacher List -->
            <VBox styleClass="chat-sidebar" prefWidth="300.0" spacing="10.0">
               <children>
                  <Label styleClass="sidebar-title" text="👨‍🏫 Đồng nghiệp cùng môn">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  
                  <TextField fx:id="searchField" promptText="🔍 Tìm kiếm đồng nghiệp..." styleClass="search-field" />
                  
                  <ListView fx:id="teacherListView" styleClass="contact-list" VBox.vgrow="ALWAYS">
                     <placeholder>
                        <Label text="Đang tải danh sách đồng nghiệp..." styleClass="placeholder-text" />
                     </placeholder>
                  </ListView>
                  
                  <HBox alignment="CENTER" spacing="10.0">
                     <children>
                        <Button fx:id="refreshButton" onAction="#refreshTeacherList" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
               </padding>
            </VBox>
            
            <!-- Right Panel: Chat Area -->
            <VBox styleClass="chat-main" HBox.hgrow="ALWAYS" spacing="10.0">
               <children>
                  <!-- Chat Header -->
                  <HBox alignment="CENTER_LEFT" styleClass="chat-header" spacing="15.0">
                     <children>
                        <Label fx:id="chatHeaderLabel" styleClass="chat-title" text="Chọn đồng nghiệp để bắt đầu chat">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Label fx:id="onlineStatusLabel" styleClass="online-status" text="⚫ Offline" />
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
                     </padding>
                  </HBox>
                  
                  <!-- Chat Messages Area -->
                  <ScrollPane fx:id="chatScrollPane" styleClass="chat-scroll" VBox.vgrow="ALWAYS" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
                     <content>
                        <VBox fx:id="chatArea" styleClass="chat-area" spacing="8.0">
                           <padding>
                              <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
                  
                  <!-- Message Input Area -->
                  <HBox alignment="CENTER_LEFT" styleClass="message-input-area" spacing="10.0">
                     <children>
                        <TextField fx:id="messageField" promptText="💭 Nhập tin nhắn cho đồng nghiệp..." styleClass="message-input" HBox.hgrow="ALWAYS" disable="true" onKeyPressed="#handleKeyPressed" />
                        <Button fx:id="sendButton" onAction="#sendMessage" styleClass="btn,btn-primary" text="📤 Gửi" disable="true" />
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="10.0" />
                     </padding>
                  </HBox>
               </children>
            </VBox>
         </children>
      </HBox>
   </center>
</BorderPane>

/* Grade Entry Dialog Styles */

.dialog-container {
    -fx-background-color: #ffffff;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 20, 0, 0, 5);
    -fx-min-width: 450px;
    -fx-max-width: 500px;
}

.dialog-header {
    -fx-background-color: linear-gradient(to right, #667eea, #764ba2);
    -fx-background-radius: 8px;
    -fx-padding: 15px 20px;
}

.dialog-icon {
    -fx-text-fill: white;
}

.dialog-title {
    -fx-text-fill: white;
}

.form-container {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
}

.form-label {
    -fx-text-fill: #2c3e50;
}

.form-combobox {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 12px;
    -fx-font-size: 14px;
    -fx-min-height: 40px;
}

.form-combobox:focused {
    -fx-border-color: #667eea;
    -fx-border-width: 2px;
}

.form-textfield {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 12px;
    -fx-font-size: 14px;
    -fx-min-height: 40px;
}

.form-textfield:focused {
    -fx-border-color: #667eea;
    -fx-border-width: 2px;
}

.score-range-label {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

.error-label {
    -fx-text-fill: #dc3545;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.score-preview {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #28a745;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12px 15px;
}

.preview-label {
    -fx-text-fill: #495057;
    -fx-font-size: 12px;
}

.letter-grade-label {
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.grade-point-label {
    -fx-text-fill: #007bff;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.button-container {
    -fx-padding: 15px 0 0 0;
}

.btn {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 12px 24px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-cursor: hand;
    -fx-min-width: 100px;
}

.btn-primary {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-border-color: transparent;
}

.btn-primary:hover {
    -fx-background-color: #5a6fd8;
}

.btn-primary:pressed {
    -fx-background-color: #4c63d2;
}

.btn-secondary {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-color: transparent;
}

.btn-secondary:hover {
    -fx-background-color: #5a6268;
}

.btn-secondary:pressed {
    -fx-background-color: #545b62;
}

/* Animation effects */
.form-combobox:hover,
.form-textfield:hover {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.3), 5, 0, 0, 0);
}

.score-preview {
    -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.2), 8, 0, 0, 2);
}

/* Modern Grade Entry Dialog Styles */

.modern-dialog-container {
    -fx-background-color: #ffffff;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.25), 30, 0, 0, 8);
    -fx-min-width: 520px;
    -fx-max-width: 580px;
    -fx-border-color: rgba(255,255,255,0.8);
    -fx-border-width: 1px;
    -fx-border-radius: 16px;
}

.modern-dialog-header {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -fx-background-radius: 16px 16px 0 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 3);
}

.icon-container {
    -fx-background-color: rgba(255,255,255,0.2);
    -fx-background-radius: 50px;
    -fx-padding: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 2);
}

.modern-dialog-icon {
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.5), 2, 0, 0, 1);
}

.modern-dialog-title {
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
}

.modern-dialog-subtitle {
    -fx-text-fill: rgba(255,255,255,0.9);
    -fx-font-style: italic;
}

.modern-form-container {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #ffffff);
    -fx-spacing: 20px;
}

.input-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 3);
    -fx-border-color: rgba(102, 126, 234, 0.1);
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.input-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 16, 0, 0, 4);
    -fx-border-color: rgba(102, 126, 234, 0.3);
}

.score-input-card {
    -fx-background-color: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
    -fx-background-radius: 12px;
    -fx-padding: 25px;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.2), 15, 0, 0, 4);
    -fx-border-color: linear-gradient(135deg, #667eea, #764ba2);
    -fx-border-width: 2px;
    -fx-border-radius: 12px;
}

.field-icon {
    -fx-background-color: linear-gradient(135deg, #667eea, #764ba2);
    -fx-background-radius: 20px;
    -fx-padding: 8px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 6, 0, 0, 2);
}

.score-icon {
    -fx-background-color: linear-gradient(135deg, #ffd700, #ffb347);
    -fx-background-radius: 25px;
    -fx-padding: 10px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(255, 215, 0, 0.4), 8, 0, 0, 3);
}

.modern-form-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.modern-combobox {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-padding: 12px 15px;
    -fx-font-size: 14px;
    -fx-min-height: 45px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 5, 0, 0, 2);
}

.modern-combobox:focused {
    -fx-border-color: #667eea;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.3), 8, 0, 0, 0);
}

.modern-score-textfield {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-padding: 15px 18px;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-min-height: 50px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);
    -fx-text-fill: #2c3e50;
}

.modern-score-textfield:focused {
    -fx-border-color: linear-gradient(135deg, #667eea, #764ba2);
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.4), 12, 0, 0, 0);
}

.score-hint-label {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

.score-max-label {
    -fx-text-fill: #667eea;
    -fx-background-color: rgba(102, 126, 234, 0.1);
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
}

.modern-error-label {
    -fx-text-fill: #dc3545;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-background-color: rgba(220, 53, 69, 0.1);
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-effect: dropshadow(gaussian, rgba(220, 53, 69, 0.2), 4, 0, 0, 1);
}

.modern-score-preview {
    -fx-background-color: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 100%);
    -fx-border-color: linear-gradient(135deg, #28a745, #20c997);
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.2), 12, 0, 0, 3);
}

.preview-separator {
    -fx-background-color: rgba(40, 167, 69, 0.3);
    -fx-pref-height: 2px;
}

.vertical-separator {
    -fx-background-color: rgba(40, 167, 69, 0.3);
    -fx-pref-width: 2px;
    -fx-min-height: 40px;
}

.grade-preview-item {
    -fx-background-color: rgba(255, 255, 255, 0.7);
    -fx-background-radius: 8px;
    -fx-padding: 12px 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 1);
}

.preview-title {
    -fx-text-fill: #495057;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-text-transform: uppercase;
}

.modern-letter-grade {
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
    -fx-font-size: 24px;
    -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.3), 2, 0, 0, 1);
}

.modern-grade-point {
    -fx-text-fill: #007bff;
    -fx-font-weight: bold;
    -fx-font-size: 24px;
    -fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.3), 2, 0, 0, 1);
}

.modern-button-container {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa);
    -fx-border-color: rgba(0,0,0,0.1);
    -fx-border-width: 1px 0 0 0;
}

.modern-btn {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 15px 30px;
    -fx-border-radius: 25px;
    -fx-background-radius: 25px;
    -fx-cursor: hand;
    -fx-min-width: 140px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 3);
}

.modern-btn:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 12, 0, 0, 4);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.modern-btn:pressed {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 4, 0, 0, 1);
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
}

.modern-btn-save {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-text-fill: white;
    -fx-border-color: transparent;
}

.modern-btn-save:hover {
    -fx-background-color: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.modern-btn-cancel {
    -fx-background-color: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    -fx-text-fill: white;
    -fx-border-color: transparent;
}

.modern-btn-cancel:hover {
    -fx-background-color: linear-gradient(135deg, #5a6268 0%, #495057 100%);
}

/* Advanced Animation Effects */
.modern-combobox:hover,
.modern-score-textfield:hover {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.4), 10, 0, 0, 0);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.input-card:hover .field-icon {
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

.score-input-card:hover .score-icon {
    -fx-scale-x: 1.15;
    -fx-scale-y: 1.15;
    -fx-rotate: 5;
}

/* Responsive Design */
@media (max-width: 600px) {
    .modern-dialog-container {
        -fx-min-width: 400px;
        -fx-max-width: 450px;
    }

    .modern-btn {
        -fx-min-width: 120px;
        -fx-padding: 12px 24px;
    }
}

/* Dialog Specific Styles */
.dialog-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.dialog-pane .content {
    -fx-background-color: transparent;
    -fx-padding: 0;
}

.dialog-pane .button-bar {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa);
    -fx-border-color: rgba(0,0,0,0.1);
    -fx-border-width: 1px 0 0 0;
    -fx-padding: 20px 30px;
}

/* Hover Effects for Dialog Elements */
.modern-combobox:hover {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.4), 10, 0, 0, 0);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.modern-score-textfield:hover {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.4), 10, 0, 0, 0);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* Input Card Hover Effects */
.input-card:hover .field-icon {
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

.score-input-card:hover .score-icon {
    -fx-scale-x: 1.15;
    -fx-scale-y: 1.15;
    -fx-rotate: 5;
}

/* Button Hover Effects */
.modern-btn:hover {
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 12, 0, 0, 4);
}

.modern-btn:pressed {
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 4, 0, 0, 1);
}

/* Accessibility */
.modern-combobox:focused,
.modern-score-textfield:focused {
    -fx-border-width: 3px;
}

/* Loading State */
.loading {
    -fx-opacity: 0.7;
    -fx-cursor: wait;
}

/* Success State */
.success-state {
    -fx-background-color: rgba(40, 167, 69, 0.1);
    -fx-border-color: #28a745;
}

/* Error State */
.error-state {
    -fx-background-color: rgba(220, 53, 69, 0.1);
    -fx-border-color: #dc3545;
}

/* Animation Classes */
@keyframes fadeIn {
    from { -fx-opacity: 0; -fx-scale-x: 0.9; -fx-scale-y: 0.9; }
    to { -fx-opacity: 1; -fx-scale-x: 1; -fx-scale-y: 1; }
}

@keyframes slideUp {
    from { -fx-translate-y: 20; -fx-opacity: 0; }
    to { -fx-translate-y: 0; -fx-opacity: 1; }
}

.fade-in {
    -fx-animation: fadeIn 0.3s ease-out;
}

.slide-up {
    -fx-animation: slideUp 0.4s ease-out;
}

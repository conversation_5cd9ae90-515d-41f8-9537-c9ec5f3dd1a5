/* Modern Teacher Grades Interface Styles */

/* Header Styles */
.modern-teacher-header {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 15, 0, 0, 5);
}

.modern-nav-bar {
    -fx-background-color: rgba(255,255,255,0.1);
    -fx-background-radius: 0 0 20px 20px;
    -fx-border-color: rgba(255,255,255,0.2);
    -fx-border-width: 0 0 1px 0;
}

.modern-nav-button {
    -fx-background-color: rgba(255,255,255,0.2);
    -fx-background-radius: 20px;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 2);
}

.modern-nav-button:hover {
    -fx-background-color: rgba(255,255,255,0.3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.back-button {
    -fx-background-color: rgba(255,255,255,0.9);
    -fx-text-fill: #667eea;
}

.back-button:hover {
    -fx-background-color: white;
    -fx-text-fill: #5a6fd8;
}

.header-icon {
    -fx-background-color: rgba(255,255,255,0.2);
    -fx-background-radius: 25px;
    -fx-padding: 8px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 2);
}

.modern-header-title {
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
}

.modern-header-subtitle {
    -fx-text-fill: rgba(255,255,255,0.8);
    -fx-font-style: italic;
}

.modern-welcome-label {
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
}

.scale-indicator {
    -fx-text-fill: rgba(255,255,255,0.7);
    -fx-background-color: rgba(255,255,255,0.1);
    -fx-background-radius: 10px;
    -fx-padding: 3px 8px;
}

/* Content Container */
.modern-content-container {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #ffffff);
}

/* Action Section */
.action-section {
    -fx-spacing: 15px;
}

.quick-actions-card {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 20px 25px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 3);
    -fx-border-color: rgba(102, 126, 234, 0.1);
    -fx-border-width: 1px;
    -fx-border-radius: 15px;
}

.quick-actions-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 16, 0, 0, 4);
    -fx-border-color: rgba(102, 126, 234, 0.2);
}

.section-title {
    -fx-text-fill: #2c3e50;
}

.section-subtitle {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

/* Action Buttons */
.modern-action-btn {
    -fx-font-weight: bold;
    -fx-padding: 12px 25px;
    -fx-border-radius: 20px;
    -fx-background-radius: 20px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 3);
}

.modern-action-btn:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 12, 0, 0, 4);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.primary-action {
    -fx-background-color: linear-gradient(135deg, #667eea, #764ba2);
    -fx-text-fill: white;
}

.secondary-action {
    -fx-background-color: linear-gradient(135deg, #6c757d, #5a6268);
    -fx-text-fill: white;
}

/* Filter Card */
.filter-card {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 20px 25px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 10, 0, 0, 2);
    -fx-border-color: rgba(0, 123, 255, 0.1);
    -fx-border-width: 1px;
    -fx-border-radius: 15px;
}

.filter-icon {
    -fx-background-color: linear-gradient(135deg, #007bff, #0056b3);
    -fx-background-radius: 20px;
    -fx-padding: 8px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.3), 6, 0, 0, 2);
}

.filter-title {
    -fx-text-fill: #2c3e50;
}

.filter-label {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

.modern-filter-combo {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-padding: 10px 12px;
    -fx-font-size: 13px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.modern-filter-combo:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.3), 6, 0, 0, 0);
}

.modern-search-field {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-padding: 10px 15px;
    -fx-font-size: 13px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.modern-search-field:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.3), 6, 0, 0, 0);
}

.modern-search-btn {
    -fx-background-color: linear-gradient(135deg, #ffc107, #ff8f00);
    -fx-text-fill: white;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-padding: 10px 20px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(255, 193, 7, 0.3), 6, 0, 0, 2);
}

.modern-search-btn:hover {
    -fx-background-color: linear-gradient(135deg, #ff8f00, #f57c00);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Table Card */
.table-card {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 3);
    -fx-border-color: rgba(0,0,0,0.05);
    -fx-border-width: 1px;
    -fx-border-radius: 15px;
}

.table-icon {
    -fx-background-color: linear-gradient(135deg, #28a745, #20c997);
    -fx-background-radius: 20px;
    -fx-padding: 8px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.3), 6, 0, 0, 2);
}

.table-title {
    -fx-text-fill: #2c3e50;
}

.modern-table {
    -fx-background-color: transparent;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-border-color: #e1e8ed;
    -fx-border-width: 1px;
}

.modern-table .column-header {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 12px;
    -fx-border-color: #dee2e6;
}

.modern-table .table-row-cell {
    -fx-padding: 10px;
    -fx-border-color: transparent;
}

.modern-table .table-row-cell:even {
    -fx-background-color: #f8f9fa;
}

.modern-table .table-row-cell:odd {
    -fx-background-color: white;
}

.modern-table .table-row-cell:hover {
    -fx-background-color: #e3f2fd;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 1);
}

.modern-table .table-row-cell:selected {
    -fx-background-color: #bbdefb;
}

/* Statistics Card */
.stats-card {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 20px 25px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 10, 0, 0, 2);
    -fx-border-color: rgba(40, 167, 69, 0.1);
    -fx-border-width: 1px;
    -fx-border-radius: 15px;
}

.stat-item {
    -fx-background-color: rgba(102, 126, 234, 0.05);
    -fx-background-radius: 12px;
    -fx-padding: 15px 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.stat-icon {
    -fx-text-fill: #667eea;
}

.stat-number {
    -fx-text-fill: #2c3e50;
}

.stat-label {
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
    -fx-text-transform: uppercase;
}

.stat-separator {
    -fx-background-color: rgba(0,0,0,0.1);
    -fx-pref-width: 2px;
    -fx-min-height: 60px;
}

.export-title {
    -fx-text-fill: #495057;
}

.modern-export-btn {
    -fx-background-color: linear-gradient(135deg, #28a745, #20c997);
    -fx-text-fill: white;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 12px 20px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.3), 8, 0, 0, 3);
}

.modern-export-btn:hover {
    -fx-background-color: linear-gradient(135deg, #20c997, #17a2b8);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Hover Effects */
.filter-card:hover,
.table-card:hover,
.stats-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 16, 0, 0, 4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-content-container {
        -fx-spacing: 15px;
    }
    
    .quick-actions-card,
    .filter-card,
    .table-card,
    .stats-card {
        -fx-padding: 15px 20px;
    }
}

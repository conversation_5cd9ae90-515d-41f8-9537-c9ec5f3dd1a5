<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherGradesControllerSafe">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="📝 QUẢN LÝ ĐIỂM - GIÁO VIÊN">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Giáo viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Quản lý điểm số (Thang điểm 0-10)">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="addGradeBtn" onAction="#showAddGradeDialog" styleClass="btn,btn-primary" text="✨ Nhập điểm mới" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <HBox spacing="15.0">
               <children>
                  <ComboBox fx:id="courseFilterComboBox" promptText="Chọn môn học" />
                  <ComboBox fx:id="semesterFilterComboBox" promptText="Chọn học kỳ" />
                  <TextField fx:id="searchField" promptText="Tìm kiếm sinh viên..." HBox.hgrow="ALWAYS" />
                  <Button fx:id="searchBtn" onAction="#searchGrades" styleClass="btn,btn-warning" text="🔍 Tìm kiếm" />
               </children>
            </HBox>
            
            <TableView fx:id="gradesTableView" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="studentIdColumn" prefWidth="100.0" text="MSSV" />
                  <TableColumn fx:id="studentNameColumn" prefWidth="200.0" text="👨‍🎓 Tên sinh viên" />
                  <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="📚 Môn học" />
                  <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" />
                  <TableColumn fx:id="scoreColumn" prefWidth="80.0" text="⭐ Điểm số" />
                  <TableColumn fx:id="letterGradeColumn" prefWidth="80.0" text="🏆 Xếp loại" />
                  <TableColumn fx:id="semesterColumn" prefWidth="100.0" text="📅 Học kỳ" />
                  <TableColumn fx:id="dateColumn" prefWidth="120.0" text="📝 Ngày nhập" />
                  <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="⚙️ Thao tác" />
               </columns>
            </TableView>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label text="📈" styleClass="stat-icon">
                     <font>
                        <Font size="16.0" />
                     </font>
                  </Label>
                  <Label fx:id="totalGradesLabel" text="Tổng số điểm: 0" styleClass="stat-label" />
                  <Label text="🎯" styleClass="stat-icon">
                     <font>
                        <Font size="16.0" />
                     </font>
                  </Label>
                  <Label fx:id="averageGradeLabel" text="Điểm trung bình: 0.00" styleClass="stat-label" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportBtn" onAction="#exportGrades" styleClass="btn,btn-success" text="📊 Xuất Excel" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>

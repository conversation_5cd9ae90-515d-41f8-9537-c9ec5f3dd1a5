<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      spacing="20.0" styleClass="dialog-container">
   <padding>
      <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
   </padding>
   
   <!-- Header -->
   <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="dialog-header">
      <children>
         <Label text="📝" styleClass="dialog-icon">
            <font>
               <Font size="24.0" />
            </font>
         </Label>
         <Label fx:id="dialogTitle" text="Nhập điểm mới" styleClass="dialog-title">
            <font>
               <Font name="System Bold" size="18.0" />
            </font>
         </Label>
      </children>
   </HBox>
   
   <!-- Form Content -->
   <VBox spacing="20.0" styleClass="form-container">
      <children>
         <!-- Student Selection -->
         <VBox spacing="8.0">
            <children>
               <Label text="Sinh viên:" styleClass="form-label">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Label>
               <ComboBox fx:id="studentComboBox" promptText="Chọn sinh viên..." 
                        styleClass="form-combobox" maxWidth="Infinity" />
               <Label fx:id="studentErrorLabel" styleClass="error-label" visible="false" />
            </children>
         </VBox>
         
         <!-- Course Selection -->
         <VBox spacing="8.0">
            <children>
               <Label text="Môn học:" styleClass="form-label">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Label>
               <ComboBox fx:id="courseComboBox" promptText="Chọn môn học..." 
                        styleClass="form-combobox" maxWidth="Infinity" />
               <Label fx:id="courseErrorLabel" styleClass="error-label" visible="false" />
            </children>
         </VBox>
         
         <!-- Score Input -->
         <VBox spacing="8.0">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <Label text="Điểm số:" styleClass="form-label">
                        <font>
                           <Font name="System Bold" size="14.0" />
                        </font>
                     </Label>
                     <Label text="(0 - 10)" styleClass="score-range-label">
                        <font>
                           <Font size="12.0" />
                        </font>
                     </Label>
                  </children>
               </HBox>
               <TextField fx:id="scoreField" promptText="Nhập điểm (ví dụ: 8.5)" 
                         styleClass="form-textfield" />
               <Label fx:id="scoreErrorLabel" styleClass="error-label" visible="false" />
               
               <!-- Score Preview -->
               <HBox fx:id="scorePreviewBox" alignment="CENTER_LEFT" spacing="15.0" 
                     styleClass="score-preview" visible="false">
                  <children>
                     <Label text="Xếp loại:" styleClass="preview-label" />
                     <Label fx:id="letterGradeLabel" styleClass="letter-grade-label" />
                     <Label text="Điểm hệ 4:" styleClass="preview-label" />
                     <Label fx:id="gradePointLabel" styleClass="grade-point-label" />
                  </children>
               </HBox>
            </children>
         </VBox>
         
         <!-- Semester Selection -->
         <VBox spacing="8.0">
            <children>
               <Label text="Học kỳ:" styleClass="form-label">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Label>
               <ComboBox fx:id="semesterComboBox" promptText="Chọn học kỳ..." 
                        styleClass="form-combobox" maxWidth="Infinity" />
               <Label fx:id="semesterErrorLabel" styleClass="error-label" visible="false" />
            </children>
         </VBox>
      </children>
   </VBox>
   
   <!-- Action Buttons -->
   <HBox alignment="CENTER_RIGHT" spacing="15.0" styleClass="button-container">
      <children>
         <Button fx:id="cancelButton" text="Hủy" styleClass="btn,btn-secondary" 
                onAction="#handleCancel" />
         <Button fx:id="saveButton" text="Lưu điểm" styleClass="btn,btn-primary" 
                onAction="#handleSave" />
      </children>
   </HBox>
</VBox>

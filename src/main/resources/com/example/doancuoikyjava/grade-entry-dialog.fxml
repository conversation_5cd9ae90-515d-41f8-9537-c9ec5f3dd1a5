<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.effect.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1"
      spacing="0" styleClass="modern-dialog-container">

   <!-- Animated Header with Gradient -->
   <VBox styleClass="modern-dialog-header" spacing="0">
      <padding>
         <Insets bottom="25.0" left="30.0" right="30.0" top="25.0" />
      </padding>
      <children>
         <HBox alignment="CENTER_LEFT" spacing="15.0">
            <children>
               <VBox alignment="CENTER" styleClass="icon-container">
                  <children>
                     <Label text="🎓" styleClass="modern-dialog-icon">
                        <font>
                           <Font size="32.0" />
                        </font>
                     </Label>
                  </children>
               </VBox>
               <VBox spacing="5.0">
                  <children>
                     <Label fx:id="dialogTitle" text="Nhập điểm mới" styleClass="modern-dialog-title">
                        <font>
                           <Font name="System Bold" size="22.0" />
                        </font>
                     </Label>
                     <Label text="Hệ thống quản lý điểm số hiện đại" styleClass="modern-dialog-subtitle">
                        <font>
                           <Font size="13.0" />
                        </font>
                     </Label>
                  </children>
               </VBox>
            </children>
         </HBox>
      </children>
   </VBox>
   
   <!-- Modern Form Content -->
   <VBox spacing="0" styleClass="modern-form-container">
      <padding>
         <Insets bottom="30.0" left="30.0" right="30.0" top="20.0" />
      </padding>
      <children>
         <!-- Student Selection Card -->
         <VBox spacing="12.0" styleClass="input-card">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <Label text="👨‍🎓" styleClass="field-icon">
                        <font>
                           <Font size="18.0" />
                        </font>
                     </Label>
                     <Label text="Sinh viên" styleClass="modern-form-label">
                        <font>
                           <Font name="System Bold" size="15.0" />
                        </font>
                     </Label>
                  </children>
               </HBox>
               <ComboBox fx:id="studentComboBox" promptText="🔍 Tìm và chọn sinh viên..."
                        styleClass="modern-combobox" maxWidth="Infinity" />
               <Label fx:id="studentErrorLabel" styleClass="modern-error-label" visible="false" />
            </children>
         </VBox>

         <!-- Course Selection Card -->
         <VBox spacing="12.0" styleClass="input-card">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <Label text="📚" styleClass="field-icon">
                        <font>
                           <Font size="18.0" />
                        </font>
                     </Label>
                     <Label text="Môn học" styleClass="modern-form-label">
                        <font>
                           <Font name="System Bold" size="15.0" />
                        </font>
                     </Label>
                  </children>
               </HBox>
               <ComboBox fx:id="courseComboBox" promptText="🔍 Tìm và chọn môn học..."
                        styleClass="modern-combobox" maxWidth="Infinity" />
               <Label fx:id="courseErrorLabel" styleClass="modern-error-label" visible="false" />
            </children>
         </VBox>
         
         <!-- Score Input Card - Highlight -->
         <VBox spacing="15.0" styleClass="score-input-card">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="12.0">
                  <children>
                     <Label text="⭐" styleClass="score-icon">
                        <font>
                           <Font size="20.0" />
                        </font>
                     </Label>
                     <VBox spacing="3.0">
                        <children>
                           <Label text="Điểm số" styleClass="modern-form-label">
                              <font>
                                 <Font name="System Bold" size="15.0" />
                              </font>
                           </Label>
                           <Label text="Thang điểm 0 - 10 (cho phép 1 chữ số thập phân)" styleClass="score-hint-label">
                              <font>
                                 <Font size="11.0" />
                              </font>
                           </Label>
                        </children>
                     </VBox>
                  </children>
               </HBox>

               <HBox spacing="15.0" alignment="CENTER_LEFT">
                  <children>
                     <TextField fx:id="scoreField" promptText="Ví dụ: 8.5, 9.0, 7.2..."
                               styleClass="modern-score-textfield" prefWidth="200.0" />
                     <Label text="/10" styleClass="score-max-label">
                        <font>
                           <Font name="System Bold" size="16.0" />
                        </font>
                     </Label>
                  </children>
               </HBox>

               <Label fx:id="scoreErrorLabel" styleClass="modern-error-label" visible="false" />

               <!-- Enhanced Score Preview -->
               <VBox fx:id="scorePreviewBox" spacing="12.0" styleClass="modern-score-preview" visible="false">
                  <children>
                     <Separator styleClass="preview-separator" />
                     <HBox spacing="20.0" alignment="CENTER">
                        <children>
                           <VBox alignment="CENTER" spacing="5.0" styleClass="grade-preview-item">
                              <children>
                                 <Label text="Xếp loại" styleClass="preview-title" />
                                 <Label fx:id="letterGradeLabel" styleClass="modern-letter-grade" />
                              </children>
                           </VBox>
                           <Separator orientation="VERTICAL" styleClass="vertical-separator" />
                           <VBox alignment="CENTER" spacing="5.0" styleClass="grade-preview-item">
                              <children>
                                 <Label text="Điểm hệ 4" styleClass="preview-title" />
                                 <Label fx:id="gradePointLabel" styleClass="modern-grade-point" />
                              </children>
                           </VBox>
                        </children>
                     </HBox>
                  </children>
               </VBox>
            </children>
         </VBox>
         
         <!-- Semester Selection -->
         <VBox spacing="8.0">
            <children>
               <Label text="Học kỳ:" styleClass="form-label">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Label>
               <ComboBox fx:id="semesterComboBox" promptText="Chọn học kỳ..." 
                        styleClass="form-combobox" maxWidth="Infinity" />
               <Label fx:id="semesterErrorLabel" styleClass="error-label" visible="false" />
            </children>
         </VBox>
      </children>
   </VBox>
   
   <!-- Action Buttons -->
   <HBox alignment="CENTER_RIGHT" spacing="15.0" styleClass="button-container">
      <children>
         <Button fx:id="cancelButton" text="Hủy" styleClass="btn,btn-secondary" 
                onAction="#handleCancel" />
         <Button fx:id="saveButton" text="Lưu điểm" styleClass="btn,btn-primary" 
                onAction="#handleSave" />
      </children>
   </HBox>
</VBox>

import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.model.*;

public class NullDataTest {
    public static void main(String[] args) {
        System.out.println("🔍 Testing for null data issues...");
        
        try {
            // Test GradeService
            GradeService gradeService = new GradeService();
            System.out.println("✅ GradeService created");
            
            // Test UserService
            UserService userService = new UserService();
            System.out.println("✅ UserService created");
            
            // Test getting all grades
            var allGrades = gradeService.getAllGrades();
            System.out.println("📊 Total grades: " + (allGrades != null ? allGrades.size() : "NULL"));
            
            if (allGrades != null) {
                int nullCount = 0;
                int invalidCount = 0;
                
                for (Grade grade : allGrades) {
                    if (grade == null) {
                        nullCount++;
                    } else {
                        // Check for null fields
                        if (grade.getStudentId() == null || 
                            grade.getCourseId() == null || 
                            grade.getCourseName() == null || 
                            grade.getSemester() == null || 
                            grade.getTeacherId() == null) {
                            invalidCount++;
                            System.out.println("⚠️ Invalid grade found: " + 
                                "StudentId=" + grade.getStudentId() + 
                                ", CourseId=" + grade.getCourseId() + 
                                ", CourseName=" + grade.getCourseName() + 
                                ", Semester=" + grade.getSemester() + 
                                ", TeacherId=" + grade.getTeacherId());
                        }
                    }
                }
                
                System.out.println("❌ Null grades: " + nullCount);
                System.out.println("⚠️ Invalid grades: " + invalidCount);
                System.out.println("✅ Valid grades: " + (allGrades.size() - nullCount - invalidCount));
            }
            
            // Test getting all users
            var allUsers = userService.getAllUsers();
            System.out.println("👥 Total users: " + (allUsers != null ? allUsers.size() : "NULL"));
            
            if (allUsers != null) {
                int nullUserCount = 0;
                int invalidUserCount = 0;
                
                for (User user : allUsers) {
                    if (user == null) {
                        nullUserCount++;
                    } else {
                        if (user.getUserId() == null || user.getFullName() == null) {
                            invalidUserCount++;
                            System.out.println("⚠️ Invalid user found: " + 
                                "UserId=" + user.getUserId() + 
                                ", FullName=" + user.getFullName());
                        }
                    }
                }
                
                System.out.println("❌ Null users: " + nullUserCount);
                System.out.println("⚠️ Invalid users: " + invalidUserCount);
                System.out.println("✅ Valid users: " + (allUsers.size() - nullUserCount - invalidUserCount));
            }
            
            // Test specific teacher grades
            System.out.println("\n🧪 Testing teacher grades...");
            
            // Find a teacher
            var teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            if (teachers != null && !teachers.isEmpty()) {
                Teacher teacher = (Teacher) teachers.get(0);
                System.out.println("👨‍🏫 Testing with teacher: " + teacher.getTeacherId());
                
                var teacherGrades = gradeService.getGradesByTeacher(teacher.getTeacherId());
                System.out.println("📊 Teacher grades: " + (teacherGrades != null ? teacherGrades.size() : "NULL"));
                
                if (teacherGrades != null) {
                    for (Grade grade : teacherGrades) {
                        if (grade == null) {
                            System.out.println("❌ Found null grade in teacher grades");
                        } else {
                            // Test getting student for this grade
                            User student = userService.getUserById(grade.getStudentId());
                            if (student == null) {
                                System.out.println("⚠️ Student not found for grade: " + grade.getStudentId());
                            }
                        }
                    }
                }
            } else {
                System.out.println("⚠️ No teachers found");
            }
            
            System.out.println("\n🎉 Data test completed!");
            
        } catch (Exception e) {
            System.err.println("❌ Error during data test: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

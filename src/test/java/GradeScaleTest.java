import com.example.doancuoikyjava.model.Grade;

public class GradeScaleTest {
    public static void main(String[] args) {
        System.out.println("=== Test thang điểm mới (0-10) ===");
        
        // Test các điểm số khác nhau
        double[] testScores = {10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.0, 3.0, 0.0};
        
        for (double score : testScores) {
            Grade grade = new Grade();
            grade.setScore(score);
            
            System.out.printf("Điểm %.1f -> Xếp loại: %s, <PERSON><PERSON>ể<PERSON> hệ 4: %.1f%n", 
                score, grade.getLetterGrade(), grade.getGradePoint());
        }
        
        System.out.println("\n=== Kiểm tra validation ===");
        
        // Test validation
        double[] invalidScores = {-1.0, 11.0, 15.5};
        
        for (double score : invalidScores) {
            if (score < 0 || score > 10) {
                System.out.printf("Điểm %.1f -> KHÔNG HỢP LỆ (phải từ 0-10)%n", score);
            }
        }
        
        System.out.println("\n=== Test hoàn tất ===");
    }
}
